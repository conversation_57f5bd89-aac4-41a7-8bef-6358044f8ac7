import { HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { User } from "src/user/schema/user.schema";
import { MailerService } from "@nestjs-modules/mailer";
import { SendMailContext, SendMailOptions } from "./interfaces/mail.interface";

@Injectable()
export class MailService {
    private from: string;
    private FR_BASE_URL: string;
    private FR_BASE_URL2: string;

    constructor(private readonly mailService: MailerService, private readonly configService: ConfigService) {
        this.from = this.configService.get<string>("SENDER_EMAIL");
        this.FR_BASE_URL = this.configService.get<string>("FR_BASE_URL");
        this.FR_BASE_URL2 = this.configService.get<string>("FR_BASE_URL2");
    }

    async sendMail({ from, to, subject, template }: SendMailOptions, context: SendMailContext): Promise<any> {
        try {
            const sentResponse = await this.mailService.sendMail({
                from: from,
                to: to,
                subject: subject,
                template: template,
                context,
            });
            return { sent: true, sentResponse };
        } catch (error: any) {
            return { sent: false, error: error?.message };
        }
    }

    async sendResetPasswordMail(user: User, url: string): Promise<any> {
        const name = user?.lastName ? user.firstName + " " + user.lastName : user.firstName;
        return this.sendMail(
            {
                from: this.from,
                to: user.email,
                subject: "Forgot your password? (valid for only 10 minutes)",
                template: "resetPassword",
            },
            {
                subject: "Resetting your password",
                header: "Reset your password",
                name,
                url,
            },
        );
    }

    async sendCompanyInvitationMail(
        email: string,
        company: string,
        url: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        return this.sendMail(
            {
                from: { name: company, address: this.from },
                to: email,
                subject: "You are INVITED to " + `${company}` + " by " + `${senderName}`,
                template: "invitation",
            },
            {
                subject: "Invitation",
                header: `Invitation to ${company}`,
                senderName,
                company,
                reciverName,
                url,
            },
        );
    }

    /**
     * sends a mail to user's for payment Success
     * @param user user object containing user information
     * @param url account activation URL
     */
    async sendSuccessfulPayment(companyName: string, email: string, subscriptionName: string) {
        const data = `We're delighted to confirm that your payment for the ${subscriptionName} subscription has been successfully processed!`;

        this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "stripePaymentSuccess",
            subject: "Successfully Subscribed",
            context: {
                header: "Subscription Successful",
                companyName,
                data,
            },
        });
    }

    /**
     * sends a mail to user's for payment fail
     * @param user user object containing user information
     * @param url account activation URL
     */
    async sendFailedPayment(companyName: string, email: string, subscriptionName?: string) {
        const data = `We regret to inform you that your payment for the ${subscriptionName} subscription has not been processed successfully.
        Please be aware that if this issue is not resolved, you may lose access to the features provided by your subscription. To continue enjoying uninterrupted service, we recommend updating your payment details or retrying the transaction as soon as possible.`;

        this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "stripePaymentFailed",
            subject: "Failed Payment",
            context: {
                header: "Payment Failed",
                companyName,
                data,
            },
        });
    }

    /**
     * sends a mail to user's for subscription cancel
     * @param user user object containing user information
     * @param url account activation URL
     */
    async sendSuccessfulCancel(companyName: string, email: string, subscriptionName: string) {
        const data = `Your ${subscriptionName} subscription has been successfully canceled. Your access to the subscription features will remain active until the end of your current billing period, after which it will be discontinued.`;
        this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "stripePaymentSuccess",
            subject: "Successful Cancelation",
            context: {
                header: "Subscription Canceled",
                companyName,
                data,
                // footer,
            },
        });
    }

    async supportTicketCreated(
        userName: string,
        email: string,
        subject: string,
        requestId: string,
        submissionDate: string,
        issueSummary: string,
    ) {
        try {
            await this.mailService.sendMail({
                from: { name: "Piece Work Pro Support", address: this.from },
                to: email,
                template: "supportTicketCreated",
                subject: subject,
                context: {
                    userName,
                    requestId,
                    submissionDate,
                    issueSummary,
                },
            });

            return { sent: true };
        } catch (e) {
            return { sent: false };
            // if (e instanceof HttpException) {
            //     throw e;
            // }
            // throw new InternalServerErrorException(e);
        }
    }

    async supportTicketClosed(
        userName: string,
        email: string,
        subject: string,
        requestId: string,
        resolutionDate: string,
        closedByRole: string,
    ) {
        try {
            await this.mailService.sendMail({
                from: { name: "Piece Work Pro Support", address: this.from },
                to: email,
                template: "supportTicketClosed",
                subject: subject,
                context: {
                    userName,
                    requestId,
                    resolutionDate,
                    closedByRole,
                },
            });

            return { sent: true };
        } catch (e) {
            return { sent: false };
            // if (e instanceof HttpException) {
            //     throw e;
            // }
            // throw new InternalServerErrorException(e);
        }
    }

    async sendMailToAddNewOwner(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const data = `Hi ${reciverName} Good News! You are now an admin on ${company}'s Piece Work Pro account. ${senderName} added you — be sure to thank them.
                     With great power comes great responsibility. As an admin you have access to everything in the company's account. Please use a unique password to keep your account safe.
                     Sincerely,
                     The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToAddNewOwner",
            subject: `You're now an admin on ${company}'s Piece Work Pro Account`,
            context: {
                header: "Adding new Admin",
                company,
                data,
            },
        });
    }

    async sendMailToChangeRole(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const data = `Hi ${reciverName}, You are no longer an admin on ${company}'s Piece Work Pro account. If you weren't expecting
                      this change, please contact us so we can make sure your account is secure. Sincerely,
                      The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToChangeRole",
            subject: `You're no longer an admin on ${company}'s Piece Work Pro Account`,
            context: {
                header: "Removing Admin",
                company,
                data,
            },
        });
    }

    async sendMailToOwnerForAddingNewAdmin(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const data = `Hi ${senderName} Good News! You have added ${reciverName} as a company admin on ${company}'s Piece Work Pro account. ${reciverName} 
                      has been sent an email to let them know about their new privileges. 
                      Sincerely,
                      The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToOwnerForAddingNewAdmin",
            subject: `You've added an admin on ${company}'s Piece Work Pro Account`,
            context: {
                header: "Adding new Admin",
                company,
                data,
            },
        });
    }

    async sendMailToChangeRoleToAdmin(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const data = `Hi ${senderName}, You’ve removed an admin on ${company}'s Piece Work Pro account. Their privileges have been fully revoked effective immediately.
                    If you weren’t expecting this change, please contact us so we can make sure your account is secure.
                    Sincerely,
                    The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToChangeRoleToAdmin",
            subject: `You've removed an admin on ${company}'s Piece Work Pro Account`,
            context: {
                header: "Removing Admin",
                company,
                data,
            },
        });
    }

    async sendMailToNewOwner(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const [firstName] = reciverName.split(" ");
        const data = `Hi ${firstName}, You are the new Owner of ${company} Piece Work Pro account. ${senderName} transferred ownership to you - be sure to thank them.
                      With great power comes great responsibility. As the Owner you have control of everything in the company account. Please use a unique password to keep your account safe.
                      If you werent expecting this change, please contact us so we can make sure your account is secure.
                      Sincerely,
                      The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToNewOwner",
            subject: `You've added an admin on ${company}'s Piece Work Pro Account`,
            context: {
                header: "Switch Owner",
                company,
                data,
            },
        });
    }

    async sendMailToOldOwner(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const [firstName] = senderName.split(" ");
        const [reciverFirstName] = reciverName.split(" ");
        const data = `Hi ${firstName}, You have successfully transferred ownership of ${company} Piece Work Pro account to ${reciverName}.${reciverFirstName}
                     has been sent an email to let them know about their new privileges.
                    If you werent expecting this change, please contact us so we can make sure your account is secure.
                    Sincerely,
                    The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToOldOwner",
            subject: `You're no longer the Owner of ${company}'s Piece Work Pro Account`,
            context: {
                header: "Switch Owner",
                company,
                data,
            },
        });
    }

    async sendMailToAllOldOwner(
        email: string,
        company: string,
        senderName: string,
        reciverName: string,
    ): Promise<any> {
        const [firstName] = senderName.split(" ");

        const data = `Hi ${firstName}, Ownership of ${company} Piece Work Pro account has been transferred to ${reciverName}.
        If you weren’t expecting this change, please contact us so we can make sure your account is secure.
        Sincerely,
        The Piece Work Pro team`;

        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: email,
            template: "sendMailToAllOldOwner",
            subject: `Ownership change on ${company} Piece Work Pro Account`,
            context: {
                header: "Switch Owner",
                company,
                data,
            },
        });
    }

    async sendFormSubmissionMail(recipientEmails: string[], formName: string, fileUrl: string): Promise<any> {
        if (!recipientEmails.length) {
            console.warn("No recipients to send email.");
            return;
        }

        const subject = `New Form Submission: ${formName}`;
        const body = `A new form submission for your company has been received.\n\nPlease find the attached file.\n\n`;

        // Send email with S3 attachment
        await this.mailService.sendMail({
            from: { name: "Piece Work Pro", address: this.from },
            to: recipientEmails,
            template: "formSubmission",
            subject,
            context: {
                header: "New Form Submission",
                data: body,
            },
            attachments: [
                {
                    filename: `${formName}.pdf`,
                    path: fileUrl,
                },
            ],
        });
    }

    async sendContractorSignupMail(
        email: string,
        company: string,
        companyId: string,
        reciverName: string,
        password: string,
    ): Promise<any> {
        //TODO: hardcoded value if its nhr redirect to nhrapp.com else pieceworkpro.com
        const frontendBaseUrl =
            "0f33b070-a7f2-43f3-8d07-54fdfd4378e3" === companyId ? this.FR_BASE_URL : this.FR_BASE_URL2;

        return await this.mailService.sendMail({
            from: { name: company, address: this.from },
            to: email,
            template: "contractorSingup",
            subject: `You are signed up to ${company}`,
            context: {
                company,
                reciverName,
                frontendBaseUrl,
                password,
            },
        });
    }
}
