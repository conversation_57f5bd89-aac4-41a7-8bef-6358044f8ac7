import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiBearerAuth,
    ApiInternalServerErrorResponse,
    ApiNotFoundResponse,
    ApiOperation,
    ApiTags,
    ApiUnauthorizedResponse,
} from "@nestjs/swagger";
import { Roles } from "src/auth/guards/auth.guard";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { UserRolesEnum } from "src/company/enum/role.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { CreateLeadSourceRuleDto } from "./dto/create-lead-source-rule.dto";
import { DeleteLeadSourceRuleDto } from "./dto/delete-lead-source-rule.dto";
import { RestoreLeadSourceRuleDto } from "./dto/restore-lead-source-rule.dto";
import { UpdateLeadSourceRuleDto } from "./dto/update-lead-source-rule.dto";
import { GetLeadSourceRuleDto } from "./dto/fetch-lead-source-rule.dto";
import { TestRuleDto } from "./dto/test-rule.dto";
import { LeadSourceRuleService } from "./lead-source-rule.service";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { JwtUserPayload } from "src/auth/interface/auth.interface";

@ApiBearerAuth()
@ApiTags("LeadSourceRule")
@Auth()
@Controller({ path: "lead-source-rule", version: "1" })
export class LeadSourceRuleController {
    constructor(private readonly leadSourceRuleService: LeadSourceRuleService) {}

    /**
     * Creates a new lead source rule
     * @param user - The authenticated user
     * @param createLeadSourceRuleDto - The DTO containing the data for the new rule
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Create Lead Source Rule" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Post("create")
    async createLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Body() createLeadSourceRuleDto: CreateLeadSourceRuleDto,
    ): Promise<HttpResponse> {
        return this.leadSourceRuleService.createLeadSourceRule(
            user.companyId,
            user.memberId,
            createLeadSourceRuleDto,
        );
    }

    /**
     * Get all lead source rules with pagination and filtering
     * @param user - The authenticated user
     * @param paginationDto - Pagination parameters
     * @param filterDto - Filter parameters
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Get Lead Source Rules" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("list")
    async getLeadSourceRules(
        @GetUser() user: JwtUserPayload,
        @Query() paginationDto: PaginationRequestDto,
        @Query() filterDto: GetLeadSourceRuleDto,
    ): Promise<HttpResponse> {
        return this.leadSourceRuleService.getLeadSourceRules(user.companyId, paginationDto, filterDto);
    }

    /**
     * Get a single lead source rule by ID
     * @param user - The authenticated user
     * @param ruleId - Rule ID
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Get Lead Source Rule by ID" })
    @ApiNotFoundResponse({ description: "Lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Get("id/:ruleId")
    async getLeadSourceRuleById(
        @GetUser() user: JwtUserPayload,
        @Param("ruleId", ParseUUIDPipe) ruleId: string,
    ): Promise<HttpResponse> {
        return this.leadSourceRuleService.getLeadSourceRuleById(user.companyId, ruleId);
    }

    /**
     * Update a lead source rule
     * @param user - The authenticated user
     * @param ruleId - Rule ID
     * @param updateLeadSourceRuleDto - Update data
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Update Lead Source Rule" })
    @ApiNotFoundResponse({ description: "Lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("id/:ruleId")
    async updateLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Param("ruleId", ParseUUIDPipe) ruleId: string,
        @Body() updateLeadSourceRuleDto: UpdateLeadSourceRuleDto,
    ): Promise<HttpResponse> {
        return this.leadSourceRuleService.updateLeadSourceRule(
            user.companyId,
            ruleId,
            updateLeadSourceRuleDto,
        );
    }

    /**
     * Delete a lead source rule (soft delete)
     * @param user - The authenticated user
     * @param deleteLeadSourceRuleDto - Delete data
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Delete Lead Source Rule" })
    @ApiNotFoundResponse({ description: "Lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Delete("delete")
    async deleteLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Body() deleteLeadSourceRuleDto: DeleteLeadSourceRuleDto,
    ): Promise<HttpResponse> {
        return this.leadSourceRuleService.deleteLeadSourceRule(user.companyId, deleteLeadSourceRuleDto);
    }

    /**
     * Restore a deleted lead source rule
     * @param user - The authenticated user
     * @param restoreLeadSourceRuleDto - Restore data
     * @returns A Promise that resolves to an HTTP response
     */
    @ApiOperation({ summary: "Restore Lead Source Rule" })
    @ApiNotFoundResponse({ description: "Deleted lead source rule not found" })
    @ApiInternalServerErrorResponse({ description: "Server Error" })
    @Roles(UserRolesEnum.Admin, UserRolesEnum.Owner)
    @Patch("restore")
    async restoreLeadSourceRule(
        @GetUser() user: JwtUserPayload,
        @Body() restoreLeadSourceRuleDto: RestoreLeadSourceRuleDto,
    ): Promise<HttpResponse> {
        return this.leadSourceRuleService.restoreLeadSourceRule(user.companyId, restoreLeadSourceRuleDto);
    }

    /**
     * Test a rule against sample contact data
     * @param user - The authenticated user
     * @param ruleId - Rule ID
     * @param contactData - Sample contact data
     * @returns A Promise that resolves to an HTTP response
     */
    // @ApiOperation({ summary: "Test Lead Source Rule" })
    // @ApiNotFoundResponse({ description: "Lead source rule not found" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Post(":ruleId/test")
    // async testRule(
    //     @GetUser() user: JwtUserPayload,
    //     @Param("ruleId", ParseUUIDPipe) ruleId: string,
    //     @Body() contactData: TestRuleDto,
    // ): Promise<HttpResponse> {
    //     return this.leadSourceRuleService.testRule(user.companyId, ruleId, contactData);
    // }

    /**
     * Find matching lead source for contact data
     * @param user - The authenticated user
     * @param contactData - Contact data to match
     * @returns A Promise that resolves to an HTTP response
     */
    // @ApiOperation({ summary: "Find Matching Lead Source" })
    // @ApiInternalServerErrorResponse({ description: "Server Error" })
    // @Post("match")
    // async findMatchingLeadSource(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() contactData: TestRuleDto,
    // ): Promise<HttpResponse> {
    //     return this.leadSourceRuleService.findMatchingLeadSource(user.companyId, contactData);
    // }
}
