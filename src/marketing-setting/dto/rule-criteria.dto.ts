import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsString, IsNotEmpty, IsOptional, IsBoolean } from "class-validator";
import { RuleCriteriaType, RuleOperator } from "../schema/lead-source-rule.schema";

export class RuleCriteriaDto {
    @ApiProperty({
        description:
            "Field to match against. Supports contact fields (firstName, lastName, email, etc.) and tracking fields (utmSource, utmCampaign, sessionSource, etc.)",
        enum: RuleCriteriaType,
        example: RuleCriteriaType.UTM_SOURCE,
    })
    @IsEnum(RuleCriteriaType)
    @IsNotEmpty()
    field: RuleCriteriaType;

    @ApiProperty({
        description: "Operator for matching",
        enum: RuleOperator,
        example: RuleOperator.CONTAINS,
    })
    @IsEnum(RuleOperator)
    @IsNotEmpty()
    operator: RuleOperator;

    @ApiProperty({
        description: "Value to match",
        example: "<PERSON>",
    })
    @IsString()
    @IsNotEmpty()
    value: string;

    @ApiPropertyOptional({
        description: "Whether matching should be case sensitive",
        default: false,
    })
    @IsBoolean()
    @IsOptional()
    caseSensitive?: boolean;
}
