import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsString, IsBoolean, IsUUID } from "class-validator";

export class GetLeadSourceRuleDto {
    @ApiPropertyOptional({ description: "Search by rule name" })
    @IsString()
    @Transform(({ value }) => value?.trim(), { toClassOnly: true })
    @IsOptional()
    name?: string;

    @ApiPropertyOptional({ description: "Filter by lead source ID" })
    @IsUUID()
    @IsOptional()
    leadSourceId?: string;

    @ApiPropertyOptional({ description: "Filter by active status" })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;

    @ApiPropertyOptional({ description: "Include deleted rules", default: false })
    @IsBoolean()
    @IsOptional()
    includeDeleted?: boolean;
}
