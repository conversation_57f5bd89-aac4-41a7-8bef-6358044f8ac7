import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsOptional, IsBoolean, IsUUID } from "class-validator";

export class GetLeadSourceRuleDto {
    @ApiPropertyOptional({ description: "Filter by lead source ID" })
    @IsUUID()
    @IsOptional()
    leadSourceId?: string;

    @ApiPropertyOptional({ description: "Filter by campaign ID" })
    @IsUUID()
    @IsOptional()
    campaignId?: string;

    @ApiPropertyOptional({ description: "Filter by active status" })
    @IsBoolean()
    @IsOptional()
    @Transform(({ value }) => value === "true" || value === true)
    isActive?: boolean;

    @ApiPropertyOptional({ description: "Include deleted rules", default: false })
    @IsBoolean()
    @IsOptional()
    @Transform(({ value }) => value === "true" || value === true)
    includeDeleted?: boolean;
}
