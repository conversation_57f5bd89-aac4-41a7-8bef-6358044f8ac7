import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
    IsNotEmpty,
    IsOptional,
    IsUUID,
    IsString,
    IsBoolean,
    IsArray,
    ValidateNested,
} from "class-validator";

class ConditionDto {
    @ApiProperty({ description: "Field name to match against", required: true })
    @IsString()
    @IsNotEmpty()
    field: string;

    @ApiProperty({ description: "Value to match", required: true })
    @IsString()
    @IsNotEmpty()
    value: string;
}

export class CreateLeadSourceRuleDto {
    @ApiProperty({
        description: "Conditions for matching contacts",
        type: [ConditionDto],
        required: true,
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ConditionDto)
    @IsNotEmpty()
    conditions: ConditionDto[];

    @ApiProperty({ description: "Lead source ID to assign when rule matches", required: true })
    @IsUUID()
    @IsNotEmpty()
    leadSourceId: string;

    @ApiPropertyOptional({ description: "Campaign ID to assign when rule matches" })
    // @IsUUID()
    @IsOptional()
    campaignId?: string;

    @ApiPropertyOptional({
        description: "Whether the rule is active",
        default: true,
    })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}
