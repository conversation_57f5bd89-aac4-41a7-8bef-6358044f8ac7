import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsNotEmpty, IsOptional, IsUUID, IsString, IsBoolean, IsNumber, IsArray, ValidateNested, Min } from "class-validator";
import { RuleCriteriaDto } from "./rule-criteria.dto";

export class CreateLeadSourceRuleDto {
    @ApiProperty({ description: "Rule name", required: true })
    @IsString()
    @Transform(({ value }) => value.trim(), { toClassOnly: true })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: "Rule description" })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiProperty({ 
        description: "Criteria for matching contacts",
        type: [RuleCriteriaDto],
        required: true
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => RuleCriteriaDto)
    @IsNotEmpty()
    criteria: RuleCriteriaDto[];

    @ApiProperty({ description: "Lead source ID to assign when rule matches", required: true })
    @IsUUID()
    @IsNotEmpty()
    leadSourceId: string;

    @ApiPropertyOptional({ 
        description: "Rule priority (higher number = higher priority)",
        default: 0
    })
    @IsNumber()
    @Min(0)
    @IsOptional()
    priority?: number;

    @ApiPropertyOptional({ 
        description: "Whether the rule is active",
        default: true
    })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}
