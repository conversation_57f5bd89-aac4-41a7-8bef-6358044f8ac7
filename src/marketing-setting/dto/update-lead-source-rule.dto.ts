import { ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsOptional, IsUUID, IsString, IsBoolean, IsNumber, IsArray, ValidateNested, Min } from "class-validator";
import { RuleCriteriaDto } from "./rule-criteria.dto";

export class UpdateLeadSourceRuleDto {
    @ApiPropertyOptional({ description: "Rule name" })
    @IsString()
    @Transform(({ value }) => value?.trim(), { toClassOnly: true })
    @IsOptional()
    name?: string;

    @ApiPropertyOptional({ description: "Rule description" })
    @IsString()
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ 
        description: "Criteria for matching contacts",
        type: [RuleCriteriaDto]
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => RuleCriteriaDto)
    @IsOptional()
    criteria?: RuleCriteriaDto[];

    @ApiPropertyOptional({ description: "Lead source ID to assign when rule matches" })
    @IsUUID()
    @IsOptional()
    leadSourceId?: string;

    @ApiPropertyOptional({ description: "Rule priority (higher number = higher priority)" })
    @IsNumber()
    @Min(0)
    @IsOptional()
    priority?: number;

    @ApiPropertyOptional({ description: "Whether the rule is active" })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}
