import { ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsOptional, IsUUID, IsBoolean, IsArray, ValidateNested } from "class-validator";

class ConditionDto {
    @ApiPropertyOptional({ description: "Field name to match against" })
    field?: string;

    @ApiPropertyOptional({ description: "Value to match" })
    value?: string;
}

export class UpdateLeadSourceRuleDto {
    @ApiPropertyOptional({
        description: "Conditions for matching contacts",
        type: [ConditionDto],
    })
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ConditionDto)
    @IsOptional()
    conditions?: ConditionDto[];

    @ApiPropertyOptional({ description: "Lead source ID to assign when rule matches" })
    // @IsUUID()
    @IsOptional()
    leadSourceId?: string;

    @ApiPropertyOptional({ description: "Campaign ID to assign when rule matches" })
    // @IsUUID()
    @IsOptional()
    campaignId?: string;

    @ApiPropertyOptional({ description: "Whether the rule is active" })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
}
