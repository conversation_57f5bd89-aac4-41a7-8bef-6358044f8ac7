import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean } from "class-validator";

export class TestRuleDto {
    @ApiPropertyOptional({ description: "First name" })
    @IsString()
    @IsOptional()
    firstName?: string;

    @ApiPropertyOptional({ description: "Last name" })
    @IsString()
    @IsOptional()
    lastName?: string;

    @ApiPropertyOptional({ description: "Full name" })
    @IsString()
    @IsOptional()
    fullName?: string;

    @ApiPropertyOptional({ description: "Email address" })
    @IsString()
    @IsOptional()
    email?: string;

    @ApiPropertyOptional({ description: "Phone number" })
    @IsString()
    @IsOptional()
    phone?: string;

    @ApiPropertyOptional({ description: "Street address" })
    @IsString()
    @IsOptional()
    street?: string;

    @ApiPropertyOptional({ description: "Full address" })
    @IsString()
    @IsOptional()
    fullAddress?: string;

    @ApiPropertyOptional({ description: "City" })
    @IsString()
    @IsOptional()
    city?: string;

    @ApiPropertyOptional({ description: "State" })
    @IsString()
    @IsOptional()
    state?: string;

    @ApiPropertyOptional({ description: "ZIP code" })
    @IsString()
    @IsOptional()
    zip?: string;

    @ApiPropertyOptional({ description: "Business name" })
    @IsString()
    @IsOptional()
    businessName?: string;

    @ApiPropertyOptional({ description: "Notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "Is business contact" })
    @IsBoolean()
    @IsOptional()
    isBusiness?: boolean;

    // Tracking data
    @ApiPropertyOptional({ description: "Tracking data object or array" })
    @IsOptional()
    tracking?: any;

    @ApiPropertyOptional({ description: "Raw tracking data from Zapier" })
    @IsOptional()
    rawTracking?: any;

    // Direct Zapier fields for testing
    @ApiPropertyOptional({ description: "Lead Source Description from Zapier" })
    @IsString()
    @IsOptional()
    "Lead Source Description"?: string;

    @ApiPropertyOptional({ description: "Contact source from Zapier" })
    @IsString()
    @IsOptional()
    contact_source?: string;

    @ApiPropertyOptional({ description: "Conversion Campaign from Zapier" })
    @IsString()
    @IsOptional()
    "Conversion - Campaign"?: string;

    @ApiPropertyOptional({ description: "Conversion Keywords from Zapier" })
    @IsString()
    @IsOptional()
    "Conversion - Keywords"?: string;

    @ApiPropertyOptional({ description: "Conversion Ad Set from Zapier" })
    @IsString()
    @IsOptional()
    "Conversion - Ad Set"?: string;

    @ApiPropertyOptional({ description: "Conversion Creative from Zapier" })
    @IsString()
    @IsOptional()
    "Conversion - Creative"?: string;

    @ApiPropertyOptional({ description: "Google Click ID from Zapier" })
    @IsString()
    @IsOptional()
    "Google - Click ID"?: string;
}
