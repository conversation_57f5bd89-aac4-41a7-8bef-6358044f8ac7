import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsBoolean } from "class-validator";

export class TestRuleDto {
    @ApiPropertyOptional({ description: "First name" })
    @IsString()
    @IsOptional()
    firstName?: string;

    @ApiPropertyOptional({ description: "Last name" })
    @IsString()
    @IsOptional()
    lastName?: string;

    @ApiPropertyOptional({ description: "Full name" })
    @IsString()
    @IsOptional()
    fullName?: string;

    @ApiPropertyOptional({ description: "Email address" })
    @IsString()
    @IsOptional()
    email?: string;

    @ApiPropertyOptional({ description: "Phone number" })
    @IsString()
    @IsOptional()
    phone?: string;

    @ApiPropertyOptional({ description: "Street address" })
    @IsString()
    @IsOptional()
    street?: string;

    @ApiPropertyOptional({ description: "Full address" })
    @IsString()
    @IsOptional()
    fullAddress?: string;

    @ApiPropertyOptional({ description: "City" })
    @IsString()
    @IsOptional()
    city?: string;

    @ApiPropertyOptional({ description: "State" })
    @IsString()
    @IsOptional()
    state?: string;

    @ApiPropertyOptional({ description: "ZIP code" })
    @IsString()
    @IsOptional()
    zip?: string;

    @ApiPropertyOptional({ description: "Business name" })
    @IsString()
    @IsOptional()
    businessName?: string;

    @ApiPropertyOptional({ description: "Notes" })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiPropertyOptional({ description: "Is business contact" })
    @IsBoolean()
    @IsOptional()
    isBusiness?: boolean;
}
