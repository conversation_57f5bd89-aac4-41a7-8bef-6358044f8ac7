import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateLeadSourceRuleDto } from "./dto/create-lead-source-rule.dto";
import { DeleteLeadSourceRuleDto } from "./dto/delete-lead-source-rule.dto";
import { RestoreLeadSourceRuleDto } from "./dto/restore-lead-source-rule.dto";
import { UpdateLeadSourceRuleDto } from "./dto/update-lead-source-rule.dto";
import { GetLeadSourceRuleDto } from "./dto/fetch-lead-source-rule.dto";
import { LeadSourceRuleDocument } from "./schema/lead-source-rule.schema";
import { LeadSourceDocument } from "./schema/lead-source.schema";

@Injectable()
export class LeadSourceRuleService {
    constructor(
        @InjectModel("LeadSourceRule") private readonly leadSourceRuleModel: Model<LeadSourceRuleDocument>,
        @InjectModel("LeadSource") private readonly leadSourceModel: Model<LeadSourceDocument>,
    ) {}

    /**
     * Creates a new lead source rule
     * @param companyId - Company ID
     * @param memberId - Member ID creating the rule
     * @param createLeadSourceRuleDto - Rule data
     * @returns Created response
     */
    async createLeadSourceRule(
        companyId: string,
        memberId: string,
        createLeadSourceRuleDto: CreateLeadSourceRuleDto,
    ) {
        try {
            // Verify lead source exists
            const leadSource = await this.leadSourceModel.findOne({
                _id: createLeadSourceRuleDto.leadSourceId,
                companyId,
                deleted: false,
            });

            if (!leadSource) {
                throw new HttpException("Lead source not found", HttpStatus.NOT_FOUND);
            }

            // Check if rule name already exists
            const existingRule = await this.leadSourceRuleModel.findOne({
                companyId,
                name: createLeadSourceRuleDto.name,
                deleted: false,
            });

            if (existingRule) {
                throw new HttpException("Rule with this name already exists", HttpStatus.CONFLICT);
            }

            const newRule = new this.leadSourceRuleModel({
                ...createLeadSourceRuleDto,
                companyId,
                createdBy: memberId,
            });

            await newRule.save();

            return new CreatedResponse({
                message: "Lead source rule created successfully",
                data: newRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Get all lead source rules with pagination and filtering
     * @param companyId - Company ID
     * @param paginationDto - Pagination parameters
     * @param filterDto - Filter parameters
     * @returns Paginated rules
     */
    async getLeadSourceRules(
        companyId: string,
        paginationDto: PaginationRequestDto,
        filterDto: GetLeadSourceRuleDto,
    ) {
        try {
            const { skip = 1, limit = 10 } = paginationDto;
            const { name, leadSourceId, isActive, includeDeleted = false } = filterDto;

            const filter: any = { companyId };

            if (!includeDeleted) {
                filter.deleted = false;
            }

            if (name) {
                filter.name = { $regex: name, $options: "i" };
            }

            if (leadSourceId) {
                filter.leadSourceId = leadSourceId;
            }

            if (isActive !== undefined) {
                filter.isActive = isActive;
            }

            const skips = (skip - 1) * limit;

            const [rules, total] = await Promise.all([
                this.leadSourceRuleModel
                    .find(filter)
                    .populate("leadSourceId", "name description")
                    .sort({ priority: -1, createdAt: -1 })
                    .skip(skips)
                    .limit(limit)
                    .lean(),
                this.leadSourceRuleModel.countDocuments(filter),
            ]);

            return new OkResponse({
                message: "Lead source rules retrieved successfully",
                data: {
                    rules,
                    pagination: {
                        page: skip,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit),
                    },
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Get a single lead source rule by ID
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @returns Rule data
     */
    async getLeadSourceRuleById(companyId: string, ruleId: string) {
        try {
            const rule = await this.leadSourceRuleModel
                .findOne({ _id: ruleId, companyId, deleted: false })
                .populate("leadSourceId", "name description")
                .lean();

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({
                message: "Lead source rule retrieved successfully",
                data: rule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Update a lead source rule
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @param updateLeadSourceRuleDto - Update data
     * @returns Updated rule
     */
    async updateLeadSourceRule(
        companyId: string,
        ruleId: string,
        updateLeadSourceRuleDto: UpdateLeadSourceRuleDto,
    ) {
        try {
            const rule = await this.leadSourceRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            // If updating lead source, verify it exists
            if (updateLeadSourceRuleDto.leadSourceId) {
                const leadSource = await this.leadSourceModel.findOne({
                    _id: updateLeadSourceRuleDto.leadSourceId,
                    companyId,
                    deleted: false,
                });

                if (!leadSource) {
                    throw new HttpException("Lead source not found", HttpStatus.NOT_FOUND);
                }
            }

            // If updating name, check for duplicates
            if (updateLeadSourceRuleDto.name && updateLeadSourceRuleDto.name !== rule.name) {
                const existingRule = await this.leadSourceRuleModel.findOne({
                    companyId,
                    name: updateLeadSourceRuleDto.name,
                    deleted: false,
                    _id: { $ne: ruleId },
                });

                if (existingRule) {
                    throw new HttpException("Rule with this name already exists", HttpStatus.CONFLICT);
                }
            }

            // Update only provided fields
            const updateData: any = {};
            Object.keys(updateLeadSourceRuleDto).forEach((key) => {
                if (updateLeadSourceRuleDto[key] !== undefined) {
                    updateData[key] = updateLeadSourceRuleDto[key];
                }
            });

            const updatedRule = await this.leadSourceRuleModel
                .findByIdAndUpdate(ruleId, updateData, { new: true })
                .populate("leadSourceId", "name description")
                .lean();

            return new OkResponse({
                message: "Lead source rule updated successfully",
                data: updatedRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Delete a lead source rule (soft delete)
     * @param companyId - Company ID
     * @param deleteLeadSourceRuleDto - Delete data
     * @returns No content response
     */
    async deleteLeadSourceRule(companyId: string, deleteLeadSourceRuleDto: DeleteLeadSourceRuleDto) {
        try {
            const rule = await this.leadSourceRuleModel.findOne({
                _id: deleteLeadSourceRuleDto.ruleId,
                companyId,
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            await this.leadSourceRuleModel.findByIdAndUpdate(deleteLeadSourceRuleDto.ruleId, {
                deleted: true,
            });

            return new NoContentResponse({
                message: "Lead source rule deleted successfully",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Restore a deleted lead source rule
     * @param companyId - Company ID
     * @param restoreLeadSourceRuleDto - Restore data
     * @returns Restored rule
     */
    async restoreLeadSourceRule(companyId: string, restoreLeadSourceRuleDto: RestoreLeadSourceRuleDto) {
        try {
            const rule = await this.leadSourceRuleModel.findOne({
                _id: restoreLeadSourceRuleDto.ruleId,
                companyId,
                deleted: true,
            });

            if (!rule) {
                throw new HttpException("Deleted lead source rule not found", HttpStatus.NOT_FOUND);
            }

            const restoredRule = await this.leadSourceRuleModel
                .findByIdAndUpdate(restoreLeadSourceRuleDto.ruleId, { deleted: false }, { new: true })
                .populate("leadSourceId", "name description")
                .lean();

            return new OkResponse({
                message: "Lead source rule restored successfully",
                data: restoredRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Find matching lead source for a contact based on rules
     * @param companyId - Company ID
     * @param contactData - Contact data to match against
     * @returns Lead source ID if match found, null otherwise
     */
    async findMatchingLeadSource(companyId: string, contactData: any) {
        try {
            // Get all active rules sorted by priority (highest first)
            const rules = await this.leadSourceRuleModel
                .find({
                    companyId,
                    deleted: false,
                    isActive: true,
                })
                .sort({ priority: -1, createdAt: -1 })
                .lean();

            // Check each rule in priority order
            for (const rule of rules) {
                if (this.evaluateRule(rule, contactData)) {
                    return rule.leadSourceId;
                }
            }

            return new OkResponse({
                message: "No matching lead source rule found",
                data: null,
            });
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Auto-assign lead source for Zapier leads
     * This method is specifically designed to be called from createLeadFromZapier
     * @param companyId - Company ID
     * @param contactData - Contact data with tracking information
     * @returns Lead source ID if match found, null otherwise
     */
    async autoAssignLeadSourceForZapier(companyId: string, contactData: any) {
        try {
            // Log the attempt for debugging
            console.log(`Attempting to auto-assign lead source for company: ${companyId}`);

            // Find matching lead source using existing logic
            const leadSourceId = await this.findMatchingLeadSource(companyId, contactData);

            if (leadSourceId) {
                console.log(`Lead source auto-assigned: ${leadSourceId}`);
            } else {
                console.log("No matching lead source rule found");
            }

            return new OkResponse(leadSourceId);
        } catch (error: any) {
            console.error(`Error in auto-assign lead source: ${error.message}`);
            // Don't throw error to avoid breaking the lead creation process
            return null;
        }
    }

    /**
     * Evaluate if a contact matches a rule's criteria
     * @param rule - Rule to evaluate
     * @param contactData - Contact data
     * @returns True if all criteria match
     */
    private evaluateRule(rule: any, contactData: any): boolean {
        // All criteria must match (AND logic)
        return rule.criteria.every((criterion) => this.evaluateCriterion(criterion, contactData));
    }

    /**
     * Evaluate a single criterion against contact data
     * @param criterion - Criterion to evaluate
     * @param contactData - Contact data
     * @returns True if criterion matches
     */
    private evaluateCriterion(criterion: any, contactData: any): boolean {
        const fieldValue = this.getFieldValue(criterion.field, contactData);
        const criterionValue = criterion.value;

        if (fieldValue === null || fieldValue === undefined) {
            return false;
        }

        const contactValue = criterion.caseSensitive ? fieldValue : fieldValue.toLowerCase();
        const ruleValue = criterion.caseSensitive ? criterionValue : criterionValue.toLowerCase();

        switch (criterion.operator) {
            case RuleOperator.EQUALS:
                return contactValue === ruleValue;
            case RuleOperator.CONTAINS:
                return contactValue.includes(ruleValue);
            case RuleOperator.STARTS_WITH:
                return contactValue.startsWith(ruleValue);
            case RuleOperator.ENDS_WITH:
                return contactValue.endsWith(ruleValue);
            case RuleOperator.NOT_EQUALS:
                return contactValue !== ruleValue;
            case RuleOperator.NOT_CONTAINS:
                return !contactValue.includes(ruleValue);
            default:
                return false;
        }
    }

    /**
     * Get field value from contact data
     * @param field - Field type
     * @param contactData - Contact data
     * @returns Field value as string
     */
    private getFieldValue(field: RuleCriteriaType, contactData: any): string | null {
        // Get tracking data - could be from tracking array or direct tracking object
        const tracking = contactData.tracking?.[0] || contactData.tracking || {};

        switch (field) {
            // Tracking/UTM fields
            case RuleCriteriaType.UTM_SOURCE:
                return tracking.utmSource || null;
            case RuleCriteriaType.UTM_MEDIUM:
                return tracking.utmMedium || null;
            case RuleCriteriaType.UTM_CAMPAIGN:
                return tracking.utmCampaign || null;
            case RuleCriteriaType.UTM_CONTENT:
                return tracking.utmContent || null;
            case RuleCriteriaType.UTM_TERM:
                return tracking.utmTerm || null;
            case RuleCriteriaType.UTM_KEYWORD:
                return tracking.utmKeyword || null;
            case RuleCriteriaType.UTM_MATCH_TYPE:
                return tracking.utmMatchType || null;
            case RuleCriteriaType.SESSION_SOURCE:
                return tracking.sessionSource || null;
            case RuleCriteriaType.REFERRING_WEBPAGE:
                return tracking.referringWebpage || null;
            case RuleCriteriaType.GOOGLE_CLICK_ID:
                return tracking.googleClickId || null;
            case RuleCriteriaType.AD_SET_ID:
                return tracking.adSetId || null;
            case RuleCriteriaType.AD_NAME:
                return tracking.adName || null;
            case RuleCriteriaType.AD_GROUP_ID:
                return tracking.adGroupId || null;
            case RuleCriteriaType.GA_CLIENT_ID:
                return tracking.gaClientId || null;
            case RuleCriteriaType.USER_AGENT:
                return tracking.userAgent || null;
            case RuleCriteriaType.URL:
                return tracking.url || null;
            case RuleCriteriaType.IP:
                return tracking.ip || null;
            case RuleCriteriaType.FORM:
                return tracking.form || null;
            case RuleCriteriaType.FORM_ID:
                return tracking.formId || null;
            default:
                return null;
        }
    }

    /**
     * Test a rule against sample contact data
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @param contactData - Sample contact data
     * @returns Test result
     */
    async testRule(companyId: string, ruleId: string, contactData: any) {
        try {
            const rule = await this.leadSourceRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            const matches = this.evaluateRule(rule, contactData);

            return new OkResponse({
                message: "Rule test completed",
                data: {
                    matches,
                    rule: {
                        id: rule._id,
                        name: rule.name,
                        criteria: rule.criteria,
                    },
                    contactData,
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
