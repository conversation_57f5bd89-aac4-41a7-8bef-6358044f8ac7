import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateLeadSourceRuleDto } from "./dto/create-lead-source-rule.dto";
import { UpdateLeadSourceRuleDto } from "./dto/update-lead-source-rule.dto";
import { GetLeadSourceRuleDto } from "./dto/fetch-lead-source-rule.dto";
import { LeadSourceRuleDocument } from "./schema/lead-source-rule.schema";
import { LeadSourceDocument } from "./schema/lead-source.schema";
import { CampaignDocument } from "./schema/campaign.schema";

@Injectable()
export class LeadSourceRuleService {
    constructor(
        @InjectModel("LeadSourceRule") private readonly leadSourceRuleModel: Model<LeadSourceRuleDocument>,
        @InjectModel("LeadSource") private readonly leadSourceModel: Model<LeadSourceDocument>,
        @InjectModel("Campaign") private readonly campaignModel: Model<CampaignDocument>,
    ) {}

    /**
     * Creates a new lead source rule
     * @param companyId - Company ID
     * @param memberId - Member ID creating the rule
     * @param createLeadSourceRuleDto - Rule data
     * @returns Created response
     */
    async createLeadSourceRule(
        companyId: string,
        memberId: string,
        createLeadSourceRuleDto: CreateLeadSourceRuleDto,
    ) {
        try {
            const { leadSourceId, campaignId } = createLeadSourceRuleDto;

            // Verify lead source exists
            if (leadSourceId) {
                const leadSource = await this.leadSourceModel.exists({
                    _id: createLeadSourceRuleDto.leadSourceId,
                    companyId,
                    deleted: false,
                });

                if (!leadSource) {
                    throw new HttpException("Lead source not found", HttpStatus.NOT_FOUND);
                }
            }
            if (campaignId) {
                const campaign = await this.campaignModel.exists({
                    _id: createLeadSourceRuleDto.campaignId,
                    companyId,
                    deleted: false,
                });

                if (!campaign) {
                    throw new HttpException("Campaign not found", HttpStatus.NOT_FOUND);
                }
            }

            // Verify conditions are valid
            const alreadyExistingRule = await this.leadSourceRuleModel.exists({
                companyId,
                deleted: false,
                ...(leadSourceId !== undefined && leadSourceId !== null && { leadSourceId: leadSourceId }),
                ...(campaignId !== undefined && campaignId !== null && { campaignId: campaignId }),
            });

            if (alreadyExistingRule) {
                throw new HttpException(
                    "Rule already exists for this leadsource or campaign",
                    HttpStatus.BAD_REQUEST,
                );
            }

            createLeadSourceRuleDto = this.sanitizeData(createLeadSourceRuleDto);

            // No need to check for duplicate names since the schema doesn't have a name field
            const newRule = new this.leadSourceRuleModel({
                ...createLeadSourceRuleDto,
                companyId,
                createdBy: memberId,
            });

            await newRule.save();

            return new CreatedResponse({
                message: "Lead source rule created successfully",
                data: newRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    private sanitizeData(data: any): any {
        // Handle null/undefined input
        if (data === null || data === undefined) {
            return undefined;
        }

        // Handle arrays
        if (Array.isArray(data)) {
            const filteredArray = data
                .map((item) => this.sanitizeData(item))
                .filter((item) => item !== undefined && item !== null && item !== "");
            return filteredArray.length > 0 ? filteredArray : undefined;
        }

        // Handle objects
        if (typeof data === "object") {
            const sanitizedObj = {};
            for (const [key, value] of Object.entries(data)) {
                const sanitizedValue = this.sanitizeData(value);
                if (sanitizedValue !== undefined && sanitizedValue !== null && sanitizedValue !== "") {
                    sanitizedObj[key] = sanitizedValue;
                }
            }
            return Object.keys(sanitizedObj).length > 0 ? sanitizedObj : undefined;
        }

        // Handle primitive values
        return data === "" ? undefined : data;
    }

    /**
     * Get all lead source rules with pagination and filtering
     * @param companyId - Company ID
     * @param paginationDto - Pagination parameters
     * @param filterDto - Filter parameters
     * @returns Paginated rules
     */
    async getLeadSourceRules(
        companyId: string,
        paginationDto: PaginationRequestDto,
        filterDto: GetLeadSourceRuleDto,
    ) {
        try {
            const { skip = 1, limit = 10 } = paginationDto;
            const { leadSourceId, isActive, includeDeleted = false } = filterDto;

            const filter: any = { companyId };

            if (!includeDeleted) {
                filter.deleted = false;
            }

            if (leadSourceId) {
                filter.leadSourceId = leadSourceId;
            }

            if (isActive !== undefined) {
                filter.isActive = isActive;
            }

            const skips = (skip - 1) * limit;

            const [rules, total] = await Promise.all([
                this.leadSourceRuleModel
                    .find(filter)
                    .populate("leadSourceId", "name description")
                    .sort({ createdAt: -1 })
                    .skip(skips)
                    .limit(limit)
                    .lean(),
                this.leadSourceRuleModel.countDocuments(filter),
            ]);

            return new OkResponse({
                message: "Lead source rules retrieved successfully",
                data: {
                    rules,
                    pagination: {
                        page: skip,
                        limit,
                        total,
                        totalPages: Math.ceil(total / limit),
                    },
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Get a single lead source rule by ID
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @returns Rule data
     */
    async getLeadSourceRuleById(companyId: string, ruleId: string) {
        try {
            const rule = await this.leadSourceRuleModel
                .findOne({ _id: ruleId, companyId, deleted: false })
                .populate("leadSourceId", "name description")
                .lean();

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({
                message: "Lead source rule retrieved successfully",
                data: rule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Update a lead source rule
     * @param companyId - Company ID
     * @param ruleId - Rule ID
     * @param updateLeadSourceRuleDto - Update data
     * @returns Updated rule
     */
    async updateLeadSourceRule(
        companyId: string,
        ruleId: string,
        updateLeadSourceRuleDto: UpdateLeadSourceRuleDto,
    ) {
        try {
            const { leadSourceId, campaignId } = updateLeadSourceRuleDto;

            // Verify rule exists
            const rule = await this.leadSourceRuleModel.findOne({
                _id: ruleId,
                companyId,
                ...(leadSourceId !== undefined && leadSourceId !== null && { leadSourceId: leadSourceId }),
                ...(campaignId !== undefined && campaignId !== null && { campaignId: campaignId }),
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            // Verify lead source exists
            const ruleWithLeadSource = await this.leadSourceRuleModel.exists({
                _id: { $ne: ruleId },
                companyId,
                ...(leadSourceId !== undefined && leadSourceId !== null && { leadSourceId: leadSourceId }),
                ...(campaignId !== undefined && campaignId !== null && { campaignId: campaignId }),
                deleted: false,
            });

            if (ruleWithLeadSource) {
                throw new HttpException(
                    "Rule already exists for this leadsource or campaign",
                    HttpStatus.BAD_REQUEST,
                );
            }

            // Update only provided fields
            const updateData: any = {};
            Object.keys(updateLeadSourceRuleDto).forEach((key) => {
                if (updateLeadSourceRuleDto[key] !== undefined) {
                    updateData[key] = updateLeadSourceRuleDto[key];
                }
            });

            const updatedRule = await this.leadSourceRuleModel.findByIdAndUpdate(ruleId, updateData, {
                new: true,
            });

            return new OkResponse({
                message: "Lead source rule updated successfully",
                data: updatedRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Delete a lead source rule (soft delete)
     * @param companyId - Company ID
     * @param deleteLeadSourceRuleDto - Delete data
     * @returns No content response
     */
    async deleteLeadSourceRule(companyId: string, ruleId: string) {
        try {
            const rule = await this.leadSourceRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: false,
            });

            if (!rule) {
                throw new HttpException("Lead source rule not found", HttpStatus.NOT_FOUND);
            }

            await this.leadSourceRuleModel.findByIdAndUpdate(ruleId, {
                deleted: true,
            });

            return new NoContentResponse({
                message: "Lead source rule deleted successfully",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Restore a deleted lead source rule
     * @param companyId - Company ID
     * @param restoreLeadSourceRuleDto - Restore data
     * @returns Restored rule
     */
    async restoreLeadSourceRule(companyId: string, ruleId: string) {
        try {
            const rule = await this.leadSourceRuleModel.findOne({
                _id: ruleId,
                companyId,
                deleted: true,
            });

            if (!rule) {
                throw new HttpException("Deleted lead source rule not found", HttpStatus.NOT_FOUND);
            }

            const restoredRule = await this.leadSourceRuleModel
                .findByIdAndUpdate(ruleId, { deleted: false }, { new: true })
                .populate("leadSourceId", "name description")
                .lean();

            return new OkResponse({
                message: "Lead source rule restored successfully",
                data: restoredRule,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Find matching lead source for a contact based on rules
     * @param companyId - Company ID
     * @param trackingData - Contact tracking data to match against
     * @returns Lead source ID if match found, null otherwise
     */
    async findMatchingLeadSource(companyId: string, tracking: any) {
        try {
            // Get all active rules sorted by creation date (newest first)
            const rules = await this.leadSourceRuleModel
                .find({
                    companyId,
                    deleted: false,
                    isActive: true,
                    conditions: { $exists: true, $ne: [] },
                })
                .select("conditions leadSourceId campaignId");

            const matchingRule = rules.find((rule) => {
                // Check if conditions array exists and is not empty
                if (!rule.conditions || rule.conditions.length === 0) {
                    return false;
                }

                return rule.conditions.every((condition) => {
                    return tracking[condition.field] === condition.value;
                });
            });

            if (matchingRule && Object.keys(matchingRule).length !== 0) {
                return {
                    trackingRuleId: matchingRule._id,
                    leadSourceId: matchingRule?.leadSourceId,
                    campaignId: matchingRule?.campaignId,
                };
            }

            return null;
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Auto-assign lead source for Zapier leads
     * This method is specifically designed to be called from createLeadFromZapier
     * @param companyId - Company ID
     * @param tracking - Contact tracking data information
     * @returns Lead source ID & campaign ID if match found, null otherwise
     */
    async autoAssignLeadSourceAndCampaignForZapier(companyId: string, tracking: any): Promise<any> {
        try {
            // Find matching lead source using existing logic
            const matchingRule = await this.findMatchingLeadSource(companyId, tracking);

            // console.log("matchingRule1", matchingRule);

            if (matchingRule) {
                // console.log(`Lead source auto-assigned: ${matchingRule}`);
                return matchingRule;
            } else {
                // console.log("No matching lead source rule found");
                return null;
            }
        } catch (error: any) {
            // console.error(`Error in auto-assign lead source: ${error.message}`);
            // Don't throw error to avoid breaking the lead creation process
            return null;
        }
    }
}
