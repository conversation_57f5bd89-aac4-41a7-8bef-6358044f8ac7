import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionSchema } from "src/position/schema/position.schema";
import { LeadSourceController } from "./lead-source.controller";
import { LeadSourceService } from "./lead-source.service";
import { LeadSourceSchema } from "./schema/lead-source.schema";
import { CampaignService } from "./campaign.service";
import { CampaignController } from "./campaign.controller";
import { CampaignSchema } from "./schema/campaign.schema";
import { MarketingChannelSchema } from "./schema/channel.schema.dto";
import { ChannelController } from "./channel.controller";
import { ChannelService } from "./channel.service";
import { LeadSourceRuleController } from "./lead-source-rule.controller";
import { LeadSourceRuleService } from "./lead-source-rule.service";
import { LeadSourceRuleSchema } from "./schema/lead-source-rule.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "LeadSource", schema: LeadSourceSchema },
            { name: "MarketingChannel", schema: MarketingChannelSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Campaign", schema: CampaignSchema },
            { name: "LeadSourceRule", schema: LeadSourceRuleSchema },
        ]),
    ],
    providers: [LeadSourceService, CampaignService, ChannelService, LeadSourceRuleService],
    controllers: [LeadSourceController, CampaignController, ChannelController, LeadSourceRuleController],
    exports: [LeadSourceService, CampaignService, ChannelService, LeadSourceRuleService],
})
export class MarketingSettingModule {}
