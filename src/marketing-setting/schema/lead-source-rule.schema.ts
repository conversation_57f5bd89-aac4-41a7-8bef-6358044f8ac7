import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type LeadSourceRuleDocument = LeadSourceRule & Document;

@Schema({ timestamps: true, id: false, collection: "LeadSourceRule", strict: true })
export class LeadSourceRule {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({
        type: [
            {
                field: String,
                value: String,
                _id: false,
            },
        ],
        required: true,
    })
    conditions: Array<{
        field: string;
        value: string | string[] | number | boolean;
    }>;

    @UUIDProp({ required: true })
    leadSourceId: string;

    // @Prop({ type: Number, default: 0 })
    // priority: number; // Higher number = higher priority

    @Prop({ type: Boolean, default: true })
    isActive: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const LeadSourceRuleSchema = SchemaFactory.createForClass(LeadSourceRule);

// Indexes for performance
LeadSourceRuleSchema.index({ companyId: 1, deleted: 1, isActive: 1 });
LeadSourceRuleSchema.index({ companyId: 1, priority: -1, deleted: 1 });
LeadSourceRuleSchema.index({ companyId: 1, leadSourceId: 1, deleted: 1 });
