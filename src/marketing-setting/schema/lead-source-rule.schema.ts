import { Schema, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type LeadSourceRuleDocument = LeadSourceRule & Document;

export enum RuleCriteriaType {
    // Tracking/UTM fields
    UTM_SOURCE = "utmSource",
    UTM_MEDIUM = "utmMedium",
    UTM_CAMPAIGN = "utmCampaign",
    UTM_CONTENT = "utmContent",
    UTM_TERM = "utmTerm",
    UTM_KEYWORD = "utmKeyword",
    UTM_MATCH_TYPE = "utmMatchType",
    SESSION_SOURCE = "sessionSource",
    REFERRING_WEBPAGE = "referringWebpage",
    GOOGLE_CLICK_ID = "googleClickId",
    AD_SET_ID = "adSetId",
    AD_NAME = "adName",
    AD_GROUP_ID = "adGroupId",
    GA_CLIENT_ID = "gaClientId",
    USER_AGENT = "userAgent",
    URL = "url",
    IP = "ip",
    FORM = "form",
    FORM_ID = "formId",
}

export enum RuleOperator {
    EQUALS = "equals",
    CONTAINS = "contains",
    STARTS_WITH = "startsWith",
    ENDS_WITH = "endsWith",
    NOT_EQUALS = "notEquals",
    NOT_CONTAINS = "notContains",
}

export interface RuleCriteria {
    field: RuleCriteriaType;
    operator: RuleOperator;
    value: string;
    caseSensitive?: boolean;
}

@Schema({ timestamps: true, id: false, collection: "LeadSourceRule", strict: true })
export class LeadSourceRule {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true })
    name: string;

    @Prop()
    description?: string;

    @Prop({
        type: [
            {
                field: { type: String, enum: Object.values(RuleCriteriaType) },
                operator: { type: String, enum: Object.values(RuleOperator) },
                value: String,
                caseSensitive: { type: Boolean, default: false },
                _id: false,
            },
        ],
        required: true,
    })
    criteria: RuleCriteria[];

    @UUIDProp({ required: true })
    leadSourceId: string;

    @Prop({ type: Number, default: 0 })
    priority: number; // Higher number = higher priority

    @Prop({ type: Boolean, default: true })
    isActive: boolean;

    @UUIDProp()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const LeadSourceRuleSchema = SchemaFactory.createForClass(LeadSourceRule);

// Indexes for performance
LeadSourceRuleSchema.index({ companyId: 1, deleted: 1, isActive: 1 });
LeadSourceRuleSchema.index({ companyId: 1, priority: -1, deleted: 1 });
LeadSourceRuleSchema.index({ companyId: 1, leadSourceId: 1, deleted: 1 });
