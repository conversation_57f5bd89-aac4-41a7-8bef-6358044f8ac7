import { HttpException, HttpStatus, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Connection, Model, Promise } from "mongoose";
import { PaginationRequestDto } from "src/company/dto/pagination-request.dto";
import { defaultSalesAction } from "src/shared/constants";
import { toCamelCase } from "src/shared/helpers/logics";
import CreatedResponse from "src/shared/http/response/created.http";
import NoContentResponse from "src/shared/http/response/no-content.http";
import OkResponse from "src/shared/http/response/ok.http";
import { CreateCheckpointDto } from "./dto/create-checkpoint.dto";
import { CreateDefaultCrmDto } from "./dto/create-default-crm.dto";
import { CreateSalesActionDto } from "./dto/create-sales-action.dto";
import { CreateStageDto } from "./dto/create-stage.dto";
import { CreateStepDto } from "./dto/create-step.dto";
import { DeleteCheckpointDto } from "./dto/delete-checkpoint.dto";
import { DeleteSalesActionDto } from "./dto/delete-sales-action.dto";
import { DeleteStageDto } from "./dto/delete-stage.dto";
import { DeleteStepDto } from "./dto/delete-step.dto";
import { UpdateCheckpointDto } from "../opportunity/dto/update-checkpoint.dto";
import { UpdateSalesActionDto } from "./dto/update-sales-action.dto";
import { UpdateStageDto } from "./dto/update-stage.dto";
import { UpdateStepDto } from "./dto/update-step.dto";
import { UpdateSequenceDto } from "./dto/updateSequence.dto";
import { FieldTypeEnum } from "./enum/field-type.enum";
import { CrmCheckpointDocument } from "./schema/crm-checkpoint.schema";
import { CrmStageDocument } from "./schema/crm-stage.schema";
import { CrmStepDocument } from "./schema/crm-step.schema";
import { SalesActionDocument } from "./schema/sales-action.schema";
import { StageRequestDto } from "./dto/get-stage.dto";
import { StepRequestDto } from "./dto/get-step.dto";
import { ActivityTypeEnum } from "./enum/activity-type.enum";
import { StageGroupEnum } from "./enum/stage-group.enum";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { OpportunityStatusEnum } from "src/opportunity/enum/opportunityStatus.enum";

@Injectable()
export class CrmService {
    constructor(
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("CrmStep") private readonly crmStepModel: Model<CrmStepDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectModel("SalesAction") private readonly salesActionModel: Model<SalesActionDocument>,
        @InjectModel("CrmCheckpoint") private readonly crmCheckpointModel: Model<CrmCheckpointDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
    ) {}

    async createStage(companyId: string, createStageDto: CreateStageDto) {
        try {
            const stage = await this.crmStageModel
                .findOne({
                    companyId,
                    name: createStageDto.name,
                    deleted: false,
                })
                .exec();
            if (stage) throw new HttpException("Stage already exists", HttpStatus.BAD_REQUEST);
            const createdStage = new this.crmStageModel({
                companyId,
                ...createStageDto,
            });
            await createdStage.save();
            return new CreatedResponse({ message: "Stage created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteStage(companyId: string, deleteStageDto: DeleteStageDto) {
        try {
            const isOppStage = await this.opportunityModel.findOne({
                companyId,
                stage: deleteStageDto.id,
                deleted: false,
            });
            if (isOppStage)
                throw new HttpException(
                    "You can't delete a stage that contains opportunities",
                    HttpStatus.BAD_REQUEST,
                );
            const result = await this.crmStageModel.updateOne(
                { _id: deleteStageDto.id, deleted: false, editable: true },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "CRM stage deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async stageOppList(companyId: string, stageId: string) {
        try {
            const opps = await this.opportunityModel.find({
                companyId,
                stage: stageId,
                deleted: false,
            });

            const oppLists = opps.reduce(
                (acc, opp) => {
                    if (opp.status === OpportunityStatusEnum.Lost) acc.lostOpp += 1;
                    if (opp.status === OpportunityStatusEnum.Active) acc.activeOpp += 1;
                    if (opp.status === OpportunityStatusEnum.Inactive) acc.inactiveOpp += 1;
                    return acc;
                },
                { lostOpp: 0, activeOpp: 0, inactiveOpp: 0 },
            );

            return new OkResponse({ ...oppLists, totalOpps: opps.length });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async migrateOppStage(companyId: string, oldStageId: string, newStageId: string) {
        try {
            const result = await this.opportunityModel.updateMany(
                {
                    companyId,
                    stage: oldStageId,
                    deleted: false,
                },
                {
                    $set: {
                        stage: newStageId,
                    },
                },
            );

            return new OkResponse({
                message: `${result.modifiedCount} opportunities migrated successfully to the new stage.`,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateStage(companyId: string, updateStageDto: UpdateStageDto) {
        try {
            const crmStage = await this.crmStageModel.findOne({ _id: updateStageDto.stageId });
            if (!crmStage) {
                throw new HttpException("Stage not found!", HttpStatus.NOT_FOUND);
            }

            // const { PMRequired, orderRequired, projectRequired, sortingField, defaultCsrId } = crmStage;
            const query = { _id: updateStageDto.stageId, companyId, deleted: false };
            // let isSortingFieldEqual = false;
            // if (sortingField && updateStageDto.sortingField) {
            //     // const [key1] = Object.keys(updateStageDto.sortingField);
            //     // const [key2] = Object.keys(sortingField);
            //     // isSortingFieldEqual =
            //     //     key1 === key2 && updateStageDto.sortingField[key1] === sortingField[key2];
            // }

            // if (
            //     updateStageDto.PMRequired === PMRequired &&
            //     updateStageDto.orderRequired === orderRequired &&
            //     updateStageDto.projectRequired === projectRequired &&
            //     isSortingFieldEqual &&
            //     updateStageDto.defaultCsrId === defaultCsrId
            // ) {
            //     query["editable"] = true;
            // }

            const result = await this.crmStageModel.updateOne(query, {
                $set: { ...updateStageDto },
            });

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Stage updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateStageSequence(userId: string, companyId: string, updateStageSequenceDto: UpdateSequenceDto) {
        const { data } = updateStageSequenceDto;

        if (!data?.length) {
            return new OkResponse({ message: "Nothing to updates!" });
        }

        try {
            const bulkOperations = data.map(({ _id, sequence }) => ({
                updateOne: {
                    filter: { _id, companyId },
                    update: { $set: { sequence } },
                },
            }));

            const result = await this.crmStageModel.bulkWrite(bulkOperations, { ordered: false });

            if (result?.modifiedCount > 0) {
                return new OkResponse({ message: "Stage sequence updated successfully" });
            }

            return new OkResponse({ message: "No sequences were updated!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getStage(userId: string, companyId: string, deleted: boolean, stageRequestDto: StageRequestDto) {
        try {
            const { stageGroup } = stageRequestDto;
            const query = {
                companyId,
                deleted,
                ...(stageGroup?.trim()
                    ? { stageGroup: { $in: stageGroup.split(",").map((group) => group.trim()) } }
                    : {}),
            };

            const limit = stageRequestDto.limit || 10;
            const offset = limit * (stageRequestDto.skip || 0);
            const stage = await this.crmStageModel
                .find(query)
                .sort({ stageGroup: -1, sequence: 1 })
                .skip(offset)
                .limit(limit);
            return new OkResponse({ stage });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getStageById(userId: string, companyId: string, stageId: string, deleted: boolean) {
        try {
            const stage = await this.crmStageModel.findOne({ _id: stageId, companyId, deleted });
            return new OkResponse({ stage });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createStep(companyId: string, createStepDto: CreateStepDto) {
        try {
            const step = await this.crmStepModel
                .findOne({
                    companyId,
                    stageId: createStepDto.stageId,
                    name: createStepDto.name,
                    deleted: false,
                })
                .exec();
            if (step) throw new HttpException("Step already exists", HttpStatus.BAD_REQUEST);
            const createdStep = new this.crmStepModel({
                companyId,
                ...createStepDto,
            });
            await createdStep.save();
            return new CreatedResponse({ message: "Step created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteStep(userId: string, deleteStepDto: DeleteStepDto) {
        try {
            const result = await this.crmStepModel.updateOne(
                { _id: deleteStepDto.id, deleted: false },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "CRM step deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateStep(userId: string, updateStepDto: UpdateStepDto) {
        let session;
        console.log(updateStepDto);
        try {
            session = await this.connection.startSession();
            session.startTransaction();
            const parentResult = await this.crmStepModel.updateOne(
                { _id: updateStepDto.stepId, deleted: false },
                { $set: { ...updateStepDto } },
                { new: true },
            );

            if (parentResult.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            // updating child steps
            await this.crmStepModel.updateMany(
                { parent: updateStepDto.stepId },
                { $set: { stageId: updateStepDto.stageId } },
                { session },
            );

            await session.commitTransaction();
            session.endSession();

            return new OkResponse({ message: "Step updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateStepSequence(userId: string, companyId: string, updateStepSequenceDto: UpdateSequenceDto) {
        const { data } = updateStepSequenceDto;

        if (!data?.length) {
            return new OkResponse({ message: "Nothing to updates!" });
        }

        try {
            const bulkOperations = data.map(({ _id, sequence }) => ({
                updateOne: {
                    filter: { _id, companyId },
                    update: { $set: { sequence } },
                },
            }));

            const result = await this.crmStepModel.bulkWrite(bulkOperations, { ordered: false });

            if (result?.modifiedCount > 0) {
                return new OkResponse({ message: "Step sequence updated successfully" });
            }

            return new OkResponse({ message: "No sequences were updated!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getStep(
        userId: string,
        companyId: string,
        stageId: string,
        deleted: boolean,
        stepRequestDto: StepRequestDto,
    ) {
        try {
            const limit = stepRequestDto.limit || 10;
            const offset = limit * (stepRequestDto.skip || 0);

            const query: any = {
                companyId,
                stageId,
                deleted,
            };

            const pTypeArray = stepRequestDto.projectTypeId
                ? stepRequestDto.projectTypeId.split(",").map((element) => element)
                : [];

            const locationArray = stepRequestDto.location
                ? stepRequestDto.location.split(",").map((element) => element)
                : [];

            if (stepRequestDto.projectTypeId || stepRequestDto.location) {
                query.$and = [];

                if (stepRequestDto.projectTypeId)
                    query.$and.push({
                        $or: [
                            { projectTypeId: [] },
                            { projectTypeId: { $in: pTypeArray } },
                            { projectTypeId: { $exists: false } },
                        ],
                    });
                if (stepRequestDto.location)
                    query.$and.push({
                        $or: [
                            { location: [] },
                            { location: { $in: locationArray } },
                            { location: { $exists: false } },
                        ],
                    });
            }

            const step = await this.crmStepModel.find(query).skip(offset).limit(limit);
            return new OkResponse({ step });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getStepById(userId: string, companyId: string, stepId: string, deleted: boolean) {
        try {
            const step = await this.crmStepModel.findOne({ _id: stepId, companyId, deleted });
            return new OkResponse({ step });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //

    // async updateOppCommission(oppCommission: UpdateOppCommissionDto, positionCheck: boolean) {
    //     if (!positionCheck) {
    //         throw new HttpException("You are unauthorized to perform this request", HttpStatus.BAD_REQUEST);
    //     }

    //     const {
    //         companyId,
    //         opportunityId,
    //         totalCommission,
    //         saleCommission,
    //         startCommission,
    //         completedCommission,
    //         date,
    //         modificationAmount,
    //         reason,
    //     } = oppCommission;

    //     try {
    //         const thirtyDaysAgo = new Date();
    //         thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);

    //         const opp = await this.opportunityModel.findOne({ _id: opportunityId, companyId });

    //         if (!opp) {
    //             throw new BadRequestException("Opportunity not found!");
    //         }

    //         const { total, sale, start, completed } = opp?.salesCommission;

    //         let newTotal = total;
    //         let newSale = sale;
    //         let newStart = start;
    //         let newCompleted = completed;

    //         if (totalCommission) {
    //             if (new Date(opp?.saleDate) < thirtyDaysAgo) {
    //                 throw new BadRequestException(
    //                     "You can't update Total commission anymore (Sale Date > 30 days)!",
    //                 );
    //             }
    //             newTotal = totalCommission;
    //             newSale = roundTo2((sale / total) * totalCommission);
    //             newStart = roundTo2((start / total) * totalCommission);
    //             newCompleted = roundTo2(totalCommission - newSale - newStart);
    //         }

    //         if (saleCommission) {
    //             if (new Date(opp?.saleDate) < thirtyDaysAgo) {
    //                 throw new BadRequestException(
    //                     "You can't update Sale commission anymore (Sale Date > 30 days)!",
    //                 );
    //             }
    //             newTotal += roundTo2(saleCommission - sale);
    //             newSale = roundTo2(saleCommission);
    //         }

    //         if (startCommission) {
    //             if (new Date(opp?.jobStartedDate) < thirtyDaysAgo) {
    //                 throw new BadRequestException(
    //                     "You can't update Job Start commission anymore (Start Date > 30 days)!",
    //                 );
    //             }
    //             newTotal += roundTo2(startCommission - start);
    //             newStart = roundTo2(startCommission);
    //         }

    //         const updateData: any = {
    //             total: newTotal,
    //             sale: newSale,
    //             start: newStart,
    //             completed: newCompleted,
    //         };

    //         if (modificationAmount) {
    //             updateData.date = new Date(date);
    //             updateData.modificationAmount = modificationAmount;
    //             updateData.reason = reason;
    //         }

    //         if (completedCommission) {
    //             if (new Date(opp?.jobCompletedDate) < thirtyDaysAgo) {
    //                 throw new BadRequestException(
    //                     "You can't update Job Completion commission anymore (Completed Date > 30 days)!",
    //                 );
    //             }
    //             newTotal += roundTo2(completedCommission - completed);
    //             newCompleted = roundTo2(completedCommission);
    //         }

    //         const result = await this.opportunityModel.updateOne(
    //             { _id: opportunityId, companyId },
    //             {
    //                 $set: {
    //                     salesCommission: updateData,
    //                 },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
    //         }

    //         return new OkResponse({ message: "Opportunity commission updated successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async addDefaultCRM(companyId: string, createDefaultCrmDto: CreateDefaultCrmDto) {
        try {
            const { repairType, replacementType } = createDefaultCrmDto.projectType;
            const newLeadId = randomUUID();
            const needsAssessmentId = randomUUID();
            const processNAId = randomUUID();
            const presentationId = randomUUID();
            const followUpId = randomUUID();
            const signingsId = randomUUID();
            const processContractId = randomUUID();
            const preparePacketId = randomUUID();
            const readyToStartId = randomUUID();
            const currentJobId = randomUUID();
            const jobFinalizationJobId = randomUUID();
            const completedJobId = randomUUID();
            const ContractCompleteStepID = randomUUID();
            const PermitReadyStepID = randomUUID();

            const defaultCrmStages = [
                {
                    _id: newLeadId,
                    companyId,
                    name: "New Lead",
                    code: "newLead",
                    editable: false,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Leads,
                },
                {
                    _id: newLeadId,
                    companyId,
                    name: "New Opportunity",
                    code: "opp",
                    editable: false,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: needsAssessmentId,
                    companyId,
                    name: "Needs Assessment",
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: processNAId,
                    companyId,
                    name: "Process NA",
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: presentationId,
                    companyId,
                    name: "Presentation",
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: followUpId,
                    companyId,
                    name: "Follow Up",
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: signingsId,
                    companyId,
                    name: "Signings",
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: processContractId,
                    companyId,
                    name: "Process Contract",
                    sequence: 7,
                    editable: false,
                    code: "processContract",
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    _id: preparePacketId,
                    companyId,
                    name: "Prepare Packet",
                    sequence: 1,
                    code: "preparePacket",
                    editable: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
                {
                    _id: readyToStartId,
                    companyId,
                    name: "Ready To Start",
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
                {
                    _id: currentJobId,
                    companyId,
                    name: "Current Job",
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
                {
                    _id: jobFinalizationJobId,
                    companyId,
                    name: "Job Finalization",
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
                {
                    _id: completedJobId,
                    companyId,
                    name: "Completed",
                    sequence: 5,
                    code: "completed",
                    editable: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
            ];
            const defaultCrmSteps = [
                {
                    companyId,
                    stageId: newLeadId,
                    name: "First Call",
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["Contact", "Left voicemail", "No voicemail", "Bad phone#"],
                    checkbox: true,
                    sequence: 1,
                    isRequire: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    activityType: ActivityTypeEnum.Call,
                },
                {
                    companyId,
                    stageId: newLeadId,
                    name: "First Text",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    isRequire: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    activityType: ActivityTypeEnum.Text,
                },
                {
                    companyId,
                    stageId: newLeadId,
                    name: "First Email",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    isRequire: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    activityType: ActivityTypeEnum.Email,
                },
                {
                    companyId,
                    stageId: needsAssessmentId,
                    name: "Appointment added to Google Calendar",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: needsAssessmentId,
                    name: "NA packet prepared",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processNAId,
                    name: "Complete sketch in RoofSnap",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processNAId,
                    name: "Create price for requested work",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processNAId,
                    name: "Print agreements",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processNAId,
                    name: "Tax Jurisdiction",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: presentationId,
                    name: "Present Options to Client",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["in-person", "phone", "email"],
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: presentationId,
                    name: "Bid left with client",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: presentationId,
                    name: "Presentation survey completed",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: presentationId,
                    name: "Follow up letter mailed",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: followUpId,
                    name: "Verbal confirmation from client",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "Fine print initialed",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "Single color choosen",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "Flashing colors chosen",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "Payment schedule confirmed",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "10% deposit collected",
                    checkbox: true,
                    isDisplay: true,
                    fieldType: FieldTypeEnum.Number,
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "Sample Collected",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: signingsId,
                    name: "Contract notes & special instructions (required)",
                    checkbox: false,
                    fieldType: FieldTypeEnum.Text,
                    sequence: 7,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    _id: ContractCompleteStepID,
                    companyId,
                    stageId: processContractId,
                    name: "Contract checked and complete",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Shingle color on contract",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: [
                        "Black Oak",
                        "Midnight Black",
                        "Storm Grey",
                        "Natural Wood",
                        "Weathered Wood",
                        "Heather",
                        "Antique Brown",
                        "Sienna Blend",
                        "Silverwood",
                        "Rainforest",
                        "TBD",
                    ],
                    parent: ContractCompleteStepID,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Drip metal color on contract",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["Black", "Brown", "White", "TBD"],
                    parent: ContractCompleteStepID,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Flashing color on contract",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["Black", "Brown", "White", "TBD"],
                    parent: ContractCompleteStepID,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Colors chosen (shingle, drip edge, flashing)",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: ContractCompleteStepID,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [repairType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Contract signed by client and NHR rep",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: ContractCompleteStepID,
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Fine print initialed by client",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: ContractCompleteStepID,
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Sales tax correct",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: ContractCompleteStepID,
                    sequence: 7,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Property Disclosure Form 1 Signed",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: ContractCompleteStepID,
                    sequence: 8,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Deposit Collected",
                    lable: "amt: $",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Number,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                // {
                //     companyId,
                //     stageId: processContractId,
                //     name: "50% down payment invoiced created",
                //     checkbox: true,
                //     isDisplay: true,
                //     fieldType: FieldTypeEnum.None,
                //     sequence: 3,
                //     createdBy: createDefaultCrmDto.createdBy,
                // },
                {
                    _id: PermitReadyStepID,
                    companyId,
                    stageId: processContractId,
                    name: "Permit ready to go",
                    lable: "Permit #:",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Number,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Permit Jurisdiction",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["Spokane Valley", "Spokane City", "Spokane County", "Liberty Lake"],
                    parent: PermitReadyStepID,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Permit Application Sent",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: PermitReadyStepID,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Permit paid",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: PermitReadyStepID,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Permit saved in client file",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    parent: PermitReadyStepID,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Project packet created",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "All dates are correct",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Check if referrer owed",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Roofsnap saved in Client File",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 7,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Needs Assessment scanned & saved in Client File",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 8,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Contract & Fine Print scanned & saved in Client File",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 9,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Property Disc. Form 1 scanned & saved in Client File",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 10,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: processContractId,
                    name: "Project Manager Assigned",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 11,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: "Client Contacted",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: "Permit Ready",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: `"Job Start" Invoice Ready`,
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: "Materials Ordered",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: "Work Order Ready",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: "Crew Folder Created",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: preparePacketId,
                    name: "Project Packet Created",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 7,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                // {
                //     companyId,
                //     stageId: preparePacketId,
                //     name: "Place In Line",
                //     checkbox: false,
                //     fieldType: FieldTypeEnum.Number,
                //     sequence: 8,
                //     createdBy: createDefaultCrmDto.createdBy,
                // },
                // {
                //     companyId,
                //     stageId: readyToStartId,
                //     name: "Place In Line",
                //     checkbox: true,
                //     fieldType: FieldTypeEnum.Number,
                //     sequence: 1,
                //     createdBy: createDefaultCrmDto.createdBy,
                // },
                {
                    companyId,
                    stageId: readyToStartId,
                    name: "Sub crew assigned",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["Yes", "No"],
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "50% down payment collected",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Number,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "City pre-inspection done",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Date,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Protection pictures taken (showing all sides of home)",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Roof deck pictures taken (showing all roof faces)",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Underlayment pictures taken (showing all roof faces)",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Foreman tear off inspection done",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Final roof pictures taken (showing all roof faces)",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 7,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Foreman roof inspection done",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 8,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "PM final inspection done",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Date,
                    sequence: 9,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: currentJobId,
                    name: "Invoice summary entered",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Date,
                    sequence: 10,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "City final inspection done",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Date,
                    sequence: 1,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Final Payment Collected",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 2,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Referral Flyer Delivered",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 3,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Malarkey Warranty Registered",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 4,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Guarantee Certificate Sent",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 5,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Property Disclosure Form 2 Delivered",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 6,
                    createdBy: createDefaultCrmDto.createdBy,
                    projectTypeId: [replacementType],
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Send Review Link?",
                    checkbox: true,
                    fieldType: FieldTypeEnum.Dropdown,
                    dropDownOptions: ["yes", "no"],
                    sequence: 7,
                    createdBy: createDefaultCrmDto.createdBy,
                },
                {
                    companyId,
                    stageId: jobFinalizationJobId,
                    name: "Confirm all docs are saved in Client File",
                    checkbox: true,
                    fieldType: FieldTypeEnum.None,
                    sequence: 8,
                    createdBy: createDefaultCrmDto.createdBy,
                },
            ];
            const defaultCrmCheckpoint = [
                {
                    companyId,
                    name: "New Lead",
                    symbol: "newLeadDate",
                    sequence: 1,
                    editable: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageDisplay: [
                        needsAssessmentId,
                        processNAId,
                        presentationId,
                        followUpId,
                        signingsId,
                        processContractId,
                    ],
                    stageEditable: [newLeadId],
                    stageSet: [],
                    stageGroup: StageGroupEnum.Leads,
                },
                {
                    companyId,
                    name: "Opportunity",
                    symbol: "oppDate",
                    sequence: 1,
                    editable: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageDisplay: [
                        needsAssessmentId,
                        processNAId,
                        presentationId,
                        followUpId,
                        signingsId,
                        processContractId,
                    ],
                    stageEditable: [newLeadId],
                    stageSet: [],
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    companyId,
                    name: "Needs Assessment",
                    symbol: "needsAssessmentDate",
                    sequence: 2,
                    editable: true,
                    stageDisplay: [processNAId, presentationId, followUpId, signingsId, processContractId],
                    stageEditable: [needsAssessmentId],
                    stageSet: [newLeadId],
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    companyId,
                    name: "Presentation",
                    symbol: "presentationDate",
                    sequence: 3,
                    editable: true,
                    stageDisplay: [followUpId, signingsId, processContractId],
                    stageEditable: [presentationId],
                    stageSet: [needsAssessmentId, processNAId],
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                // {
                //     companyId,
                //     name: "Signing Set",
                //     symbol: "signedDate",
                //     sequence: 4,
                //     editable: true,
                //     stageDisplay: [],
                //     stageEditable: [processContractId],
                //     stageSet: [followUpId, signingsId],
                //     createdBy: createDefaultCrmDto.createdBy,
                //     stageGroup: StageGroupEnum.Sales,
                // },
                {
                    companyId,
                    name: "Sale",
                    symbol: "saleDate",
                    sequence: 4,
                    stageDisplay: [
                        preparePacketId,
                        readyToStartId,
                        currentJobId,
                        jobFinalizationJobId,
                        completedJobId,
                    ],
                    stageEditable: [],
                    stageSet: [processContractId],
                    editable: false,
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Sales,
                },
                {
                    companyId,
                    name: "Job Started",
                    symbol: "jobStartedDate",
                    sequence: 1,
                    editable: false,
                    stageDisplay: [jobFinalizationJobId, completedJobId],
                    stageEditable: [currentJobId],
                    stageSet: [readyToStartId],
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
                {
                    companyId,
                    name: "Job Completed",
                    symbol: "jobCompletedDate",
                    sequence: 2,
                    editable: false,
                    stageDisplay: [completedJobId],
                    stageEditable: [jobFinalizationJobId],
                    stageSet: [currentJobId],
                    createdBy: createDefaultCrmDto.createdBy,
                    stageGroup: StageGroupEnum.Operations,
                },
            ];
            await this.crmStageModel.insertMany(defaultCrmStages);
            await this.crmStepModel.insertMany(defaultCrmSteps);
            await this.crmCheckpointModel.insertMany(defaultCrmCheckpoint);
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createCheckpoint(companyId: string, createCheckpointDto: CreateCheckpointDto) {
        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();
            // checking if stage is there or not
            // const stage = await this.crmStageModel
            //     .findOne({
            //         companyId: createCheckpointDto.companyId,
            //         name: { $regex: `\\b${createCheckpointDto.name}\\b`, $options: "i" },
            //         deleted: false,
            //     })
            //     .exec();

            // if (!stage) throw new HttpException("Stage does not exists of this name", HttpStatus.BAD_REQUEST);
            // const symbolName = `${createCheckpointDto.name.toLowerCase().replace(/\s+/g, "")}Date`;
            const symbolName = `${toCamelCase(createCheckpointDto.name)}Date`;

            const checkpoint = await this.crmCheckpointModel
                .findOne({
                    companyId,
                    name: createCheckpointDto.name,
                    symbol: symbolName,
                    stageGroup: createCheckpointDto.stageGroup,
                    deleted: false,
                })
                .exec();
            if (checkpoint) throw new HttpException("Checkpoint already exists", HttpStatus.BAD_REQUEST);

            // updating sales sequence position to last
            const allCheckpoints = await this.crmCheckpointModel
                .find({
                    companyId,
                    stageGroup: createCheckpointDto.stageGroup,
                    deleted: false,
                })
                .sort({ sequence: -1 });
            await this.crmCheckpointModel.updateOne(
                { _id: allCheckpoints[0]._id },
                {
                    $set: {
                        sequence: allCheckpoints.length + 1,
                    },
                },
                { session },
            );
            //saving new checkpoint
            const createdCheckpoint = new this.crmCheckpointModel({
                companyId,
                symbol: symbolName,
                ...createCheckpointDto,
            });
            await createdCheckpoint.save({ session });

            // ending session of mongodb
            await session.commitTransaction();
            session.endSession();

            return new CreatedResponse({
                checkpoint: createdCheckpoint,
                message: "Checkpoint created successfully!",
            });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteCheckpoint(userId: string, deleteCheckpointDto: DeleteCheckpointDto) {
        try {
            const result = await this.crmCheckpointModel.updateOne(
                { _id: deleteCheckpointDto.id, deleted: false, editable: true },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "CRM Checkpoint deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async premDeleteCheckpoint(companyId: string, deleteCheckpointDto: DeleteCheckpointDto) {
        try {
            const result = await this.crmCheckpointModel.deleteOne({
                _id: deleteCheckpointDto.id,
                companyId,
            });

            if (result.deletedCount === 0) {
                return new OkResponse({ message: "Failed to Delete Checkpoint!" });
            }

            return new NoContentResponse({ message: "CRM Checkpoint deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async premDeleteStep(companyId: string, deleteStepDto: DeleteStepDto) {
        try {
            const result = await this.crmStepModel.deleteOne({
                _id: deleteStepDto.id,
                companyId,
            });

            if (result.deletedCount === 0) {
                return new OkResponse({ message: "Failed to Delete Step!" });
            }

            return new NoContentResponse({ message: "CRM Step deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreCheckpoint(userId: string, deleteCheckpointDto: DeleteCheckpointDto) {
        try {
            const result = await this.crmCheckpointModel.updateOne(
                { _id: deleteCheckpointDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to restore Checkpoint!" });
            }

            return new NoContentResponse({ message: "CRM Checkpoint restore successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreStage(userId: string, deleteStageDto: DeleteStageDto) {
        try {
            const result = await this.crmStageModel.updateOne(
                { _id: deleteStageDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to restore Stage!" });
            }

            return new NoContentResponse({ message: "CRM Stage restore successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreStep(userId: string, deleteStepDto: DeleteStepDto) {
        try {
            const result = await this.crmStepModel.updateOne(
                { _id: deleteStepDto.id, deleted: true },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount === 0) {
                return new OkResponse({ message: "Failed to restore Step!" });
            }

            return new NoContentResponse({ message: "CRM Step restore successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCheckpoint(userId: string, updateCheckpointDto: UpdateCheckpointDto) {
        try {
            const query = { _id: updateCheckpointDto.checkpointId, deleted: false };
            const checkpoint = await this.crmCheckpointModel.findOne(query);

            // now not updating symbol on update
            // const symbol = updateCheckpointDto.name
            //     ? `${updateCheckpointDto.name.toLowerCase().replace(/\s+/g, "")}Date`
            //     : checkpoint.symbol;
            const update = checkpoint.editable
                ? { ...updateCheckpointDto }
                : {
                      stageDisplay: updateCheckpointDto.stageDisplay,
                      stageEditable: updateCheckpointDto.stageEditable,
                      stageSet: updateCheckpointDto.stageSet,
                      requiredStage: updateCheckpointDto.requiredStage,
                      isDisplay: updateCheckpointDto.isDisplay,
                  };

            const result = await this.crmCheckpointModel.updateOne(query, { $set: update });

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Checkpoint updated successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCheckpointSequence(
        userId: string,
        companyId: string,
        updateCheckpointSequenceDto: UpdateSequenceDto,
    ) {
        const { data } = updateCheckpointSequenceDto;

        if (!data?.length) {
            return new OkResponse({ message: "Nothing to updates!" });
        }

        try {
            const bulkOperations = data.map(({ _id, sequence }) => ({
                updateOne: {
                    filter: { _id, companyId },
                    update: { $set: { sequence } },
                },
            }));

            const result = await this.crmCheckpointModel.bulkWrite(bulkOperations, { ordered: false });

            if (result?.modifiedCount > 0) {
                return new OkResponse({ message: "Checkpoint sequence updated successfully" });
            }

            return new OkResponse({ message: "No sequences were updated!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCheckpoint(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
        stageGroupInput?: StageGroupEnum[],
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);

            const query = {
                companyId,
                deleted,
                ...(stageGroupInput &&
                    stageGroupInput.length > 0 && { stageGroup: { $in: stageGroupInput } }),
            };

            const [checkpoint, total] = await Promise.all([
                this.crmCheckpointModel.find(query).sort({ sequence: -1 }).skip(offset).limit(limit),
                this.crmCheckpointModel.countDocuments(query),
            ]);
            return new OkResponse({ checkpoint, total });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCheckpointById(companyId: string, checkpointId: string, deleted: boolean) {
        try {
            const checkpoint = await this.crmCheckpointModel.findOne({
                _id: checkpointId,
                companyId,
                deleted,
            });
            return new OkResponse({ checkpoint });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createSalesAction(companyId: string, createSalesActionDto: CreateSalesActionDto) {
        try {
            const { memberId, action } = createSalesActionDto;

            // Check if a sales action already exists for the member
            const existingSalesAction = await this.salesActionModel
                .findOne({ companyId, memberId, deleted: false })
                .exec();

            if (existingSalesAction) {
                // Check if the action already exists
                // const actionExists = existingSalesAction.actions.some(
                //     (existing) => existing.name === action.name,
                // );

                // if (!actionExists) {
                // Append the new action
                await this.salesActionModel.updateOne(
                    { _id: existingSalesAction._id },
                    { $push: { actions: action } },
                );
                return new CreatedResponse({ message: "New action added successfully!" });
                // } else {
                //     return new CreatedResponse({ message: "Action already exists!" });
                // }
            }

            // for default actions we have same companyId and memberId
            const defaultActions = await this.salesActionModel
                .findOne({ companyId, memberId: companyId, deleted: false })
                .exec();

            const salesAction = new this.salesActionModel({
                companyId,
                actions: [...defaultActions.actions, action],
                memberId,
            });

            await salesAction.save();
            return new CreatedResponse({ message: "Sales action created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(
                error.message || "An error occurred while creating Sales Action",
            );
        }
    }

    async deleteSalesAction(companyId: string, deleteSalesActionDto: DeleteSalesActionDto) {
        try {
            const { actionId } = deleteSalesActionDto;

            const existingSalesAction = await this.salesActionModel.findOne({
                "actions._id": actionId,
                companyId,
                deleted: false,
            });

            if (!existingSalesAction) {
                throw new HttpException("Sales action not found or already deleted!", HttpStatus.NOT_FOUND);
            }

            const result = await this.salesActionModel.updateOne(
                { "actions._id": actionId, companyId, deleted: false },
                { $pull: { actions: { _id: actionId } } },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to delete action!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "Sales action deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateSalesAction(companyId: string, updateSalesActionDto: UpdateSalesActionDto) {
        try {
            const { actionId, name, type } = updateSalesActionDto;

            const existingSalesAction = await this.salesActionModel.findOne({
                "actions._id": actionId,
                deleted: false,
                memberId: updateSalesActionDto.memberId,
                companyId,
            });

            if (!existingSalesAction) {
                throw new HttpException("Sales action not found!", HttpStatus.NOT_FOUND);
            }

            const result = await this.salesActionModel.updateOne(
                { "actions._id": actionId, memberId: updateSalesActionDto.memberId, companyId },
                {
                    $set: {
                        "actions.$.name": name,
                        "actions.$.type": type,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Sales action updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSalesAction(userId: string, companyId: string, deleted: boolean) {
        try {
            const salesActions = await this.salesActionModel.aggregate([
                {
                    $match: {
                        companyId,
                        deleted,
                        memberId: { $exists: true, $ne: null },
                        actions: { $exists: true, $not: { $size: 0 } },
                        $expr: { $ne: ["$memberId", "$companyId"] },
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "memberId",
                        foreignField: "_id",
                        as: "memberData",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$memberData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
            ]);

            return new OkResponse({ salesActions });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSalesActionById(userId: string, companyId: string, memberId: string, deleted: boolean) {
        try {
            const salesAction = await this.salesActionModel.findOne({
                memberId,
                companyId,
                deleted: false,
            });

            return new OkResponse({ salesAction });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addDefaultSalesActionSetting(companyId: string, memberId: string) {
        try {
            const actions = defaultSalesAction.map((value) => ({
                _id: randomUUID(), // Generate unique ID for each action
                name: value.name,
                type: value.type,
                default: true,
            }));

            const salesAction = new this.salesActionModel({
                companyId,
                memberId,
                actions, // Store actions correctly without extra fields
            });

            await salesAction.save();

            return new CreatedResponse({ message: "SalesAction Setting created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
