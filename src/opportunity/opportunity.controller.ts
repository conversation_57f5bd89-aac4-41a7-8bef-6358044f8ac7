import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from "@nestjs/common";
import {
    ApiOperation,
    ApiInternalServerErrorResponse,
    ApiTags,
    ApiNotFoundResponse,
    ApiUnauthorizedResponse,
    ApiBearerAuth,
    ApiResponse,
} from "@nestjs/swagger";
import { Auth, GetUser } from "src/auth/decorator/auth.decorator";
import { Positions } from "src/auth/guards/auth.guard";
import { JwtUserPayload } from "src/auth/interface/auth.interface";
import { moduleNames } from "src/shared/constants/constant";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import HttpResponse from "src/shared/http/response/response.http";
import { PositionService } from "src/position/position.service";
import { OpportunityService } from "./opportunity.service";
import { AddModificationCommissionDto } from "./dto/add-modification.dto";
import { UpdateModificationCommissionDto } from "./dto/update-modification.dto";
import { AddPieceWorkDto } from "src/crm/dto/add-modified-piecework.dto";
import { ChangeOrderDto } from "./dto/change-order.dto";
import { DeleteOpportunityCheckpointDto } from "./dto/delete-opportunity-checkpoint.dto";
import { GetOldOpportunityDto } from "./dto/get-old-opp.dto";
import { SearchOpportunityDto } from "./dto/search-opps.dto";
import { UpdateOpportunityCheckpointDto } from "./dto/update-opportunity-checkpoint.dto";
import { UpdateCompleteStageDto } from "./dto/update-complete-stage.dto";
import { CreateNewActionDto } from "./dto/create-new-action.dto";
import { CreateOpportunityCommentDto } from "./dto/create-opportunity-comment.dto";
import { CreateOpportunityDto } from "./dto/create-opportunity.dto";
import { CreateWarrantyOpportunityDto } from "./dto/create-warranty-opportunity.dto";
import { DeleteOpportunityCommentDto } from "./dto/delete-opportunity-comment.dto";
import { DeleteOpportunityDto } from "./dto/delete-opportunity.dto";
import { GetOpportunityDto } from "./dto/get-opportunity.dto";
import { LostUnLostOpportunityDto } from "./dto/lost-unlost-opporuntiy.dto";
import { PermDeleteOpportunityDto } from "./dto/perm-delete-opportunity.dto";
import { UpdateChecklistDto } from "./dto/update-checklist.dto";
import { UpdateOppStatusDto } from "./dto/update-opp-status.dto";
import { UpdateOppActivityDto } from "./dto/update-opportunity-activity.dto";
import { UpdateOpportunityCommentDto } from "./dto/update-opportunity-comment.dto";
import { UpdateOppStageDto } from "./dto/update-opportunity-stage.dto";
import { UpdateOpportunityDto } from "./dto/update-opportunity.dto";
import { CreateOpportunityFormDto } from "./dto/create-opportunity-form.dto";
import { UpdateOpportunityFormDto } from "./dto/update-opportunity-form.dto";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";

@ApiTags("opportunity")
@Auth()
@ApiBearerAuth()
@Controller({ path: "opportunity", version: "1" })
@ApiUnauthorizedResponse({ description: "Unauthorized request" })
@ApiInternalServerErrorResponse({ description: "Server Error" })
export class OpportunityController {
    constructor(
        private readonly opportunityService: OpportunityService,
        private readonly positionService: PositionService,
    ) {}
    /**
     * Creates a new opportunity for sales
     * @param userId -  User ID of the user creating the opportunity
     * @param createOpportunityDto - DTO containing the information for the new opp
     * @returns Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create Opportunity" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunityInfo,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Post()
    async createOpportunity(
        @GetUser() user: JwtUserPayload,
        @Body() createOpportunityDto: CreateOpportunityDto,
    ): Promise<HttpResponse> {
        //TODO need to update this logic for position check
        // const { response: positionCheck } = await this.positionService.checkPositionPermission(
        //     user._id,
        //     user.companyId,
        //     ["RRTech"],
        // );
        return this.opportunityService.createOpportunity(
            user.memberId,
            user.companyId,
            createOpportunityDto,
            // false, // for convert
            false, // for warranty type
        );
    }

    // /**
    //  * Convert Lead to Opportunity
    //  * @param userId -  User ID of the user creating the opportunity
    //  * @param createOpportunityDto - DTO containing the information for the new opp
    //  * @returns Promise that resolves to an HTTP response indicating the result of the operation
    //  */
    // @ApiOperation({ summary: "Convert Lead to Opportunity" })
    // @Positions({
    //     category: "crm",
    //     name: moduleNames.crm.leads,
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    // })
    // @Post("convert-to-opportunity")
    // async convertOpportunity(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() createOpportunityDto: CreateOpportunityDto,
    // ): Promise<HttpResponse> {
    //     //TODO need to update this logic for position check
    //     // const { response: positionCheck } = await this.positionService.checkPositionPermission(
    //     //     user._id,
    //     //     user.companyId,
    //     //     ["RRTech"],
    //     // );
    //     return this.opportunityService.createOpportunity(
    //         user.memberId,
    //         user.companyId,
    //         createOpportunityDto,
    //         // positionCheck,
    //         // true, // for convert lead to opp
    //         false, // for warranty type
    //     );
    // }

    /**
     * Create Warraty Opportunity for operations
     * @param userId -  User ID of the user creating the opportunity
     * @param createOpportunityDto - DTO containing the information for the new opp
     * @returns Promise that resolves to an HTTP response indicating the result of the operation
     */
    @ApiOperation({ summary: "Create Warraty Opportunity for operations" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunityInfo,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Post("warranty")
    async warrantyOpportunity(
        @GetUser() user: JwtUserPayload,
        @Body() createOpportunityDto: CreateWarrantyOpportunityDto,
    ): Promise<HttpResponse> {
        //TODO need to update this logic for position check
        // const { response: positionCheck } = await this.positionService.checkPositionPermission(
        //     user._id,
        //     user.companyId,
        //     ["RRTech"],
        // );
        return this.opportunityService.createOpportunity(
            user.memberId,
            user.companyId,
            createOpportunityDto,
            // positionCheck,
            // false, // for convert lead to opp
            true, // for warranty type
        );
    }

    /**
     * Update a opp with the given user ID and opp update data.
     * @param userId - The ID of the user making the request.
     * @param updateOpportunityDto - The opp update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunityInfo,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch()
    async updateOpportunity(
        @GetUser() user: JwtUserPayload,
        @Body() updateOpportunityDto: UpdateOpportunityDto,
    ): Promise<HttpResponse> {
        //TODO need to update this logic for position check
        // const { response: positionCheck } = await this.positionService.checkPositionPermission(
        //     user._id,
        //     user.companyId,
        //     ["SalesPerson", "RRTech"],
        // );

        return this.opportunityService.updateOpportunity(user.companyId, updateOpportunityDto, user);
    }

    /**
     * It find the not used num for given PO.
     * @param PO - The opp PO.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Get Num for given Opportunity PO" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Get("po-num/:po/:warrantyType")
    async getNum(
        @GetUser() user: JwtUserPayload,
        @Param("po") po: string,
        @Param("warrantyType") warrantyType: boolean,
    ): Promise<HttpResponse> {
        return this.opportunityService.getNum(user.companyId, po, warrantyType);
    }

    /**
     * Get opp for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get opp for
     * @param deleted - Whether to include deleted opp in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of opp for the specified Company
     */
    @ApiOperation({ summary: "Get opportunity" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.sales,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("sales/deleted/:deleted")
    async getSalesOpportunity(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getOpportunityDto: GetOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.getOpportunity(
            user.companyId,
            user.memberId,
            user.teamPermission,
            deleted,
            StageGroupEnum.Sales,
            getOpportunityDto,
        );
    }

    @ApiOperation({ summary: "Get Opportunities completion percent" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.sales,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("sales-completion-percent/deleted/:deleted")
    async getOppSalesCompletionPercent(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getOpportunityDto: GetOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.getOppCompletionPercent(
            user.companyId,
            user.memberId,
            user.teamPermission,
            deleted,
            StageGroupEnum.Sales,
            getOpportunityDto,
        );
    }

    /**
     * Get opp for a company
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get opp for
     * @param deleted - Whether to include deleted opp in the results
     * @param paginationRequestDto - Pagination request details (offset, limit, etc.)
     * @returns List of opp for the specified Company
     */
    @ApiOperation({ summary: "Get opportunity" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.operations,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("operations/deleted/:deleted")
    async getOperationsOpportunity(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getOpportunityDto: GetOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.getOpportunity(
            user.companyId,
            user.memberId,
            user.teamPermission,
            deleted,
            StageGroupEnum.Operations,
            getOpportunityDto,
            user.symbol,
        );
    }

    @ApiOperation({ summary: "Get Opportunities completion percent" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.operations,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("operations-completion-percent/deleted/:deleted")
    async getOppOperationsCompletionPercent(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
        @Query() getOpportunityDto: GetOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.getOppCompletionPercent(
            user.companyId,
            user.memberId,
            user.teamPermission,
            deleted,
            StageGroupEnum.Operations,
            getOpportunityDto,
            user.symbol,
        );
    }

    /**
     * Get opp for a company by opp id
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get opp for
     * @param opportunityId - the ID of the opp to retrieve
     * @param deleted - whether to include deleted opp in the results
     * @returns a Promise representing the HTTP response with the requested opp
     */
    @ApiOperation({ summary: "Get opportunity by id" })
    @Get("id/:opportunityId/deleted/:deleted")
    async getOpportunityById(
        @GetUser() user: JwtUserPayload,
        @Param("opportunityId", ParseUUIDPipe) opportunityId: string,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        const { response: isSalesPerson } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["SalesPerson"],
        );
        return this.opportunityService.getOpportunityById(
            user.memberId,
            user.companyId,
            opportunityId,
            deleted,
            isSalesPerson,
        );
    }

    /**
     * Complete a stage
     * @param userId - The ID of the user making the request.
     * @param updateCompleteStageDto - The opp stage update data.
     * @returns -  A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Complete Stage" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("complete-stage")
    async completeStage(
        @GetUser() user: JwtUserPayload,
        @Body() updateCompleteStageDto: UpdateCompleteStageDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.completeStage(user.companyId, updateCompleteStageDto);
    }

    /**
     * Update Opp date
     * @param userId - The ID of the user making the request.
     * @param updateOpportunityDateDto - The opp date update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    // @ApiOperation({ summary: "Update Opportunity Date" })
    // @ApiNotFoundResponse({ description: "Opportunity not found" })
    // @Positions({
    //     category: "crm",
    //     name: moduleNames.crm.opportunity,
    //     actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    // })
    // @Patch("update-opportunity-date")
    // async updateOpportunityDate(
    //     @GetUser() user: JwtUserPayload,
    //     @Body() updateOpportunityDateDto: UpdateOpportunityDateDto,
    // ): Promise<HttpResponse> {
    //     return this.opportunityService.updateOpportunityDate(user.companyId, updateOpportunityDateDto);
    // }

    /**
     * Update opp to make it lost
     * @param userId - The ID of the user making the request.
     * @param lostUnLostOpportunityDto - The DTO containing information to lost opp.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Lost Opportunity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("lost")
    async lostOpp(
        @GetUser() user: JwtUserPayload,
        @Body() lostUnLostOpportunityDto: LostUnLostOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.lostOpp(user.companyId, lostUnLostOpportunityDto);
    }

    /**
     * Update opp to make it unlost
     * @param userId - The ID of the user making the request.
     * @param lostUnLostOpportunityDto - The DTO containing information to unlost opp.
     * @returns A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "UnLost Opportunity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("un-lost")
    async unLostOpp(
        @GetUser() user: JwtUserPayload,
        @Body() lostUnLostOpportunityDto: LostUnLostOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.unLostOpp(user.companyId, lostUnLostOpportunityDto);
    }

    /**
     * Change opp status
     * @param userId - The ID of the user making the request.
     * @param updateOppStatusDto - The DTO containing information to update opp status.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Change Status Opportunity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("status")
    async oppChangeStatus(
        @GetUser() user: JwtUserPayload,
        @Body() updateOppStatusDto: UpdateOppStatusDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.oppChangeStatus(user.companyId, user.memberId, updateOppStatusDto);
    }

    /**
     * Delete a opp
     * @param userId - The ID of the user making the request.
     * @param deleteOpportunityDto - The DTO containing information to delete opp.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Opportunity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Delete("soft")
    async deleteOpportunity(
        @GetUser() user: JwtUserPayload,
        @Body() deleteOpportunityDto: DeleteOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.deleteOpportunity(user.companyId, deleteOpportunityDto);
    }

    /**
     * Perm Delete a opp
     * @param userId - The ID of the user making the request.
     * @param permDeleteOpportunityDto - The DTO containing information to perm delete opp.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Perm Delete Opportunity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Delete("permanently")
    async permDeleteOpportunity(
        @GetUser() user: JwtUserPayload,
        @Body() permDeleteOpportunityDto: PermDeleteOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.permDeleteOpportunity(user.companyId, permDeleteOpportunityDto);
    }

    /**
     * Restore deleted opportunity
     * @param id - The ID of the opportunity to restore.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Restore a soft-deleted opportunity" })
    @ApiResponse({ status: 200, description: "Opportunity restored successfully." })
    @ApiResponse({ status: 404, description: "Deleted opportunity not found." })
    @Patch("restore/:id")
    async restoreLead(
        @Param("id", ParseUUIDPipe) id: string,
        @GetUser() user: JwtUserPayload,
    ): Promise<HttpResponse> {
        return this.opportunityService.restoreOpportunity(id, user.companyId);
    }

    /**
     * Add activity to opp
     * @param userId - The ID of the user making the request.
     * @param updateOppActivityDto - The DTO containing information to add opp activity.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity Activity" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("activity")
    async updateOpportunityActivity(
        @GetUser() user: JwtUserPayload,
        @Body() updateOppActivityDto: UpdateOppActivityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateOpportunityActivity(user.companyId, updateOppActivityDto);
    }

    /**
     * Get activity of opp
     * @param userId - The ID of the user making the request.
     * @param oppId - The oppId containing Id to get opp activity.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Get Opportunity Activity" })
    @Get("activity/oppId/:oppId")
    async getOpportunityActivity(@Param("oppId") oppId: string): Promise<HttpResponse> {
        return this.opportunityService.getOpportunityActivity(oppId);
    }

    /**
     * Update opp stage
     * @param userId - The ID of the user making the request.
     * @param updateOppStageDto - The DTO containing information to update opp stage data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity Stage" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("stage")
    async updateOpportunityStage(
        @GetUser() user: JwtUserPayload,
        @Body() updateOppStageDto: UpdateOppStageDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateOpportunityStage(
            user.companyId,
            user.memberId,
            updateOppStageDto,
        );
    }

    /**
     * Create new action for opportunity
     * @param userId - The ID of the user making the request.
     * @param createNewActionDto - The DTO containing information to create new action
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Create New Action" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Post("action")
    async createNewAction(
        @GetUser() user: JwtUserPayload,
        @Body() createNewActionDto: CreateNewActionDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.createNewAction(user.companyId, createNewActionDto);
    }

    /**
     * Update action to completed
     * @param userId - The ID of the user making the request.
     * @param createNewActionDto - The DTO containing information to update action
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Action" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("action")
    async completeAction(
        @GetUser() user: JwtUserPayload,
        @Body() createNewActionDto: CreateNewActionDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.completeAction(user.companyId, createNewActionDto);
    }

    /**
     * Add opp to comment
     * @param userId - The ID of the user making the request.
     * @param createOpportunityCommentDto - The DTO containing information to create opp comment
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Create New Comment" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Post("comment")
    async createOpportunityComment(
        @GetUser() user: JwtUserPayload,
        @Body() createOpportunityCommentDto: CreateOpportunityCommentDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.createOpportunityComment(user.companyId, createOpportunityCommentDto);
    }

    /**
     * Update opp comment
     * @param userId - The ID of the user making the request.
     * @param updateOpportunityCommentDto - The DTO containing information to update opp comment
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity comment" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("comment")
    async updateOpportunityComment(
        @GetUser() user: JwtUserPayload,
        @Body() updateOpportunityCommentDto: UpdateOpportunityCommentDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateOpportunityComment(user.companyId, updateOpportunityCommentDto);
    }

    /**
     * Delete Opp comment
     * @param userId - The ID of the user making the request.
     * @param deleteOpportunityCommentDto - The DTO containing information to delete opp comment
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Opportunity Comment" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Delete("comment")
    async deleteOpportunityComment(
        @GetUser() user: JwtUserPayload,
        @Body() deleteOpportunityCommentDto: DeleteOpportunityCommentDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.deleteOpportunityComment(user._id, deleteOpportunityCommentDto);
    }

    /**
     * Get all actions history for a opportunity
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get sales action for
     * @param oppId - Opportunity ID of the opportunity to retrieve
     * @returns - A Promise representing the HTTP response with the requested sales action
     */
    @ApiOperation({ summary: "Get all actions history for a opportunity" })
    @Get("action-history/opp/:oppId")
    async getActionsHistory(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
    ): Promise<HttpResponse> {
        return this.opportunityService.getActionsHistory(user.companyId, oppId);
    }

    /**
     * Get all display steps for a opportunity
     * @param userId - User ID of the requester
     * @param companyId - Company ID of the Company to get sales action for
     * @param oppId - Opportunity ID of the opportunity to retrieve
     * @returns - A Promise representing the HTTP response with the requested sales action
     */
    @ApiOperation({ summary: "Get all display steps for a opportunity" })
    @Get("display-steps/opp/:oppId")
    async getStepsToDisplayOnOpp(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
    ): Promise<HttpResponse> {
        return this.opportunityService.getStepsToDisplayOnOpp(user.companyId, oppId);
    }

    /**
     * Add modification to opp commission
     * @param userId - The ID of the user making the request.
     * @param AddModificationCommissionDto - The DTO containing information to update opp commission
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Add Opportunity commission" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Post("commission")
    async addModificationCommission(
        @GetUser() user: JwtUserPayload,
        @Body() modCommission: AddModificationCommissionDto,
    ): Promise<HttpResponse> {
        //TODO need to update this logic for position check
        const { response: positionCheck } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["Owner", "Admin", "GeneralManager", "SalesManager"],
        );

        return this.opportunityService.addModificationCommission(
            modCommission,
            user.companyId,
            user.memberId,
            positionCheck,
        );
    }

    /**
     * Get modified opp commission
     * @param userId - The ID of the user making the request.
     * @param oppId - The ID of the opp commission
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity commission" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("commission/oppId/:oppId")
    async getModificationCommission(
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @GetUser() user: JwtUserPayload,
    ): Promise<HttpResponse> {
        return this.opportunityService.getModificationCommission(oppId, user.companyId);
    }

    /**
     * Update opp commission modification
     * @param userId - The ID of the user making the request.
     * @param UpdateOppCommissionDto - The DTO containing information to update opp commission
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity commission" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("commission")
    async updateModificationCommission(
        @GetUser() user: JwtUserPayload,
        @Body() modCommission: UpdateModificationCommissionDto,
    ): Promise<HttpResponse> {
        //TODO need to update this logic for position check
        const { response: positionCheck } = await this.positionService.checkPositionPermission(
            user._id,
            user.companyId,
            ["Owner", "Admin", "GeneralManager", "SalesManager"],
        );

        return this.opportunityService.updateModificationCommission(
            modCommission,
            user.companyId,
            user.memberId,
            positionCheck,
        );
    }

    /**
     * Update opp chekpoint
     * @param userId - The ID of the user making the request.
     * @param updateOppCheckpointDto - The DTO containing information to update opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update Opportunity Checkpoint" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("checkpoint")
    async updateOpportunityCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() updateOppCheckpointDto: UpdateOpportunityCheckpointDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateOpportunityCheckpoint(user.companyId, updateOppCheckpointDto);
    }

    /**
     * Delete opp checkpoint
     * @param userId - The ID of the user making the request.
     * @param deleteOppCheckpointDto - The DTO containing information to delete opp checkpoint
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Delete Opportunity Checkpoint" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Delete("checkpoint")
    async deleteOpportunityCheckpoint(
        @GetUser() user: JwtUserPayload,
        @Body() deleteOppCheckpointDto: DeleteOpportunityCheckpointDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.deleteOpportunityCheckpoint(user.companyId, deleteOppCheckpointDto);
    }

    @ApiOperation({ summary: "Update piece work for a opportunity" })
    @Patch("piece-work/opp/:oppId")
    async addModifiedPieceWork(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Body() addPieceWorkDto: AddPieceWorkDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.addModifiedPieceWork(user.companyId, oppId, addPieceWorkDto);
    }

    @ApiOperation({ summary: "Add change order for a opportunity" })
    @Post("change-order/opp/:oppId")
    async addChangeOrder(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Body() changeOrderDto: ChangeOrderDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.addChangeOrder(user.companyId, user.memberId, oppId, changeOrderDto);
    }

    @ApiOperation({ summary: "Update change order for a opportunity" })
    @Patch("change-order/opp/:oppId")
    async updateChangeOrder(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Body() changeOrderDto: ChangeOrderDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateChangeOrder(
            user.companyId,
            user.memberId,
            oppId,
            changeOrderDto,
        );
    }

    @ApiOperation({ summary: "Get old Opportunities" })
    @Get("old")
    async oldOpportunities(
        @GetUser() user: JwtUserPayload,
        @Query() getOldOpportunityDto: GetOldOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.oldOpportunities(user.companyId, getOldOpportunityDto);
    }

    @ApiOperation({ summary: "Update job note of opp" })
    @Patch("job-note/oppId/:oppId/note/:note")
    async updateJobNoteInOpps(
        @GetUser() user: JwtUserPayload,
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Param("note") note: string,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateJobNoteInOpps(oppId, user.companyId, note);
    }

    /**
     * Update a opp checklist with the given user ID and opp checklist update data.
     * @param userId - The ID of the user making the request.
     * @param updateChecklistDto - The opp update data.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Update checklist" })
    @ApiNotFoundResponse({ description: "Opportunity not found" })
    @Positions({
        category: "crm",
        name: moduleNames.crm.opportunity,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Patch("step-checklist")
    async updateChecklist(
        @GetUser() user: JwtUserPayload,
        @Body() updateChecklistDto: UpdateChecklistDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateChecklist(user._id, updateChecklistDto);
    }

    @ApiOperation({ summary: "Get Opportunities step checklist for a stage" })
    @Get("step-checklist/oppId/:oppId/stageId/:stageId")
    async getOpportunityStepChecklistByStage(
        @Param("oppId", ParseUUIDPipe) oppId: string,
        @Param("stageId", ParseUUIDPipe) stageId: string,
    ): Promise<HttpResponse> {
        return this.opportunityService.getOpportunityStepChecklistByStage(oppId, stageId);
    }

    /**
     * get search list of opps using search
     * @param companyId - The ID of the company making the request.
     * @param search - The text to search.
     * @returns - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "opps Search" })
    @ApiNotFoundResponse({ description: "Opp not found" })
    @Get("search")
    async oppsSearch(
        @GetUser() user: JwtUserPayload,
        @Query() searchOppDto: SearchOpportunityDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.oppsSearch(user.companyId, searchOppDto);
    }

    /**
     * api to get count of time a person is referred a opp
     * @param userId of loged-in user
     * @param companyId - The ID of the company making the request.
     * @returns  - A promise that resolves to an HTTP response.
     */
    @ApiOperation({ summary: "Referrer Count" })
    @ApiNotFoundResponse({ description: "Opp not found" })
    @Get("referrer-count")
    async referrerCount(@GetUser() user: JwtUserPayload): Promise<HttpResponse> {
        return this.opportunityService.referrerCount(user.companyId);
    }

    @Post("form")
    @ApiOperation({ summary: "Submit an Opportunity Form" })
    @ApiResponse({ status: 201, description: "Form submitted successfully" })
    async createOpportunityForm(
        @Body() createOpportunityFormDto: CreateOpportunityFormDto,
        @GetUser() user: JwtUserPayload,
    ): Promise<HttpResponse> {
        return this.opportunityService.createOpportunityForm(
            user.companyId,
            user.memberId,
            createOpportunityFormDto,
        );
    }

    @Patch("form/id/:_id")
    @ApiOperation({ summary: "Update an Opportunity Form" })
    @ApiResponse({ status: 200, description: "Opportunity Form updated" })
    async updateOpportunityForm(
        @Param("_id") _id: string,
        @Body() updateOpportunityFormDto: UpdateOpportunityFormDto,
    ): Promise<HttpResponse> {
        return this.opportunityService.updateOpportunityForm(_id, updateOpportunityFormDto);
    }

    @Get("form/id/:_id")
    @ApiOperation({ summary: "Get a specific form of a opportunity by _id" })
    @ApiResponse({ status: 200, description: "Opportunity form retrieved successfully" })
    async getOpportunityForm(@Param("_id") _id: string): Promise<HttpResponse> {
        return this.opportunityService.getOpportunityForm(_id);
    }

    @Delete("form/id/:_id")
    @ApiOperation({ summary: "Delete a Opportunity form" })
    @ApiResponse({ status: 200, description: "Opportunity form deleted successfully" })
    @ApiResponse({ status: 404, description: "Opportunity form not found" })
    async deleteOpportunityForm(@Param("_id") _id: string): Promise<HttpResponse> {
        return this.opportunityService.deleteOpportunityForm(_id);
    }

    @Positions({
        category: "module",
        name: moduleNames.module.forms,
        actions: [PermissionsEnum.Full, PermissionsEnum.Managed, PermissionsEnum.Self],
    })
    @Get("form/all/deleted/:deleted")
    @ApiOperation({ summary: "Get all Opportunity Forms submitted by the logged-in user" })
    @ApiResponse({ status: 200, description: "List of Opportunity Forms retrieved successfully" })
    async getMyOpportunityForms(
        @GetUser() user: JwtUserPayload,
        @Param("deleted") deleted: boolean,
    ): Promise<HttpResponse> {
        return this.opportunityService.getMyOpportunityForms(
            user.memberId,
            user.companyId,
            user?.teamPermission,
            deleted,
        );
    }
}
