import { IsString, <PERSON><PERSON>otEmpty, <PERSON>A<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class CreateOpportunityFormDto {
    @ApiPropertyOptional()
    @IsUUID()
    @IsOptional()
    oppId?: string;

    @ApiProperty()
    @IsUUID()
    @IsNotEmpty()
    builderFormId: string;

    @ApiProperty({ type: [Object] })
    @IsArray()
    fields?: Record<string, any>[];

    @ApiProperty()
    @IsUUID()
    @IsNotEmpty()
    mediaId: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    mediaUrl: string;

    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    locationImage?: string;

    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    name: string;
}
