import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MediaService } from "./media.service";
import { MediaController, OpenMediaController } from "./media.controller";
import { S3Module } from "src/s3/s3.module";
import { MongooseModule } from "@nestjs/mongoose";
import { FormSchema } from "src/opportunity/schema/form.schema";
import { RoleSchema } from "src/role/schema/role.schema";
import { MediaSchema } from "./schema/media.schema";
import { ShareMediaSchema } from "./schema/share-media.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "ShareMedia", schema: ShareMediaSchema },
            { name: "Forms", schema: FormSchema },
            { name: "Role", schema: RoleSchema },
            { name: "Media", schema: MediaSchema },
        ]),
        S3Module,
    ],

    controllers: [MediaController, OpenMediaController],
    providers: [MediaService],
})
export class MediaModule {}
