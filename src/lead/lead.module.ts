import { Modu<PERSON> } from "@nestjs/common";
import { LeadService } from "./lead.service";
import { Lead<PERSON>ontroller, LeadController<PERSON><PERSON><PERSON><PERSON> } from "./lead.controller";
import { LeadSchema } from "./schema/lead.schema";
import { MongooseModule } from "@nestjs/mongoose";
import { ActivityLogSchema } from "src/activity-log/schema/activity-log.schema";
import { CrmModule } from "src/crm/crm.module";
import { CrmCheckpointSchema } from "src/crm/schema/crm-checkpoint.schema";
import { CrmStepSchema } from "src/crm/schema/crm-step.schema";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { OpportunityModule } from "src/opportunity/opportunity.module";
import { ApiKeyGuard } from "src/auth/guards/api-key.guard";
import { AdminModule } from "src/admin/admin.module";
import { CrmStageSchema } from "src/crm/schema/crm-stage.schema";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { ContactSchema } from "src/contacts/schema/contact.schema";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Lead", schema: LeadSchema },
            { name: "ActivityLog", schema: ActivityLogSchema },
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "CrmCheckpoint", schema: CrmCheckpointSchema },
            { name: "CrmStep", schema: CrmStepSchema },
            { name: "CrmStage", schema: CrmStageSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "Contact", schema: ContactSchema },
        ]),
        CrmModule,
        AdminModule,
        OpportunityModule,
    ],
    controllers: [LeadController, LeadControllerApiKey],
    providers: [LeadService, ApiKeyGuard],
})
export class LeadModule {}
