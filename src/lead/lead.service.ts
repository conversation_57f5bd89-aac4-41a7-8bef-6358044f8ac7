import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import { randomUUID } from "crypto";

import { CreateLeadDto } from "./dto/create-lead.dto";
import { UpdateLeadDto } from "./dto/update-lead.dto";
import OkResponse from "src/shared/http/response/ok.http";
import HttpResponse from "src/shared/http/response/response.http";
import { LeadDocument } from "./schema/lead.schema";
import { FetchLeadDto } from "./dto/fetch-lead.dto";
import NoContentResponse from "src/shared/http/response/no-content.http";
import CreatedResponse from "src/shared/http/response/created.http";
import { formatDate, getTextChange, roundTo2 } from "src/shared/helpers/logics";
import { CreateNewLeadActionDto } from "./dto/create-action.dto";
import { CreateLeadCommentDto } from "./dto/create-lead-comment.dto";
import { UpdateLeadCommentDto } from "./dto/update-lead-comment.dto";
import { DeleteLeadCommentDto } from "./dto/delete-lead-comment.dto";
import { UpdateLeadChecklistDto } from "./dto/update-lead-checklist.dto";
import { UpdateLeadActivityDto } from "./dto/update-lead-activity.dto";
import { ActivityLogDocument } from "src/activity-log/schema/activity-log.schema";
import { UpdateLeadStatusDto } from "./dto/update-lead-status.dto";
import { CrmCheckpointDocument } from "src/crm/schema/crm-checkpoint.schema";
import { LostUnLostLeadDto } from "./dto/lost-unlost-lead.dto";
import { CrmService } from "src/crm/crm.service";
import { GetLeadDto } from "./dto/get-lead.dto";
import { CrmStepDocument } from "src/crm/schema/crm-step.schema";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { UpdateOpportunityCheckpointDto } from "src/opportunity/dto/update-opportunity-checkpoint.dto";
import { UpdateOppStageDto } from "src/opportunity/dto/update-opportunity-stage.dto";
import { DeleteOpportunityCheckpointDto } from "src/opportunity/dto/delete-opportunity-checkpoint.dto";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { OpportunityService } from "src/opportunity/opportunity.service";
import { CreateLeadFromZapierDto } from "./dto/create-lead-from-zapier.dto";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { PositionService } from "src/position/position.service";
import { ContactDocument } from "src/contacts/schema/contact.schema";
import { ContactTypeEnum } from "src/contacts/enum/contact.enum";
import { OpportunityStatusEnum } from "src/opportunity/enum/opportunityStatus.enum";

@Injectable()
export class LeadService {
    constructor(
        @InjectConnection() private readonly connection: Connection,
        private readonly crmService: CrmService,
        private readonly opportunityService: OpportunityService,
        private readonly positionService: PositionService,
        @InjectModel("Lead") private readonly leadModel: Model<LeadDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("CompanySetting")
        private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("CrmStep") private readonly crmStepModel: Model<CrmStepDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectModel("CrmCheckpoint") private readonly crmCheckpointModel: Model<CrmCheckpointDocument>,
        @InjectModel("ActivityLog") private readonly activityLogModel: Model<ActivityLogDocument>,
        @InjectModel("Contact") private readonly contactModel: Model<ContactDocument>,
    ) {}

    async createLead(createLeadDto: CreateLeadDto, companyId: string): Promise<HttpResponse> {
        try {
            // Set companyId and ensure comments array is structured
            createLeadDto["companyId"] = companyId;
            createLeadDto["createdBy"] = createLeadDto.createdBy;
            createLeadDto["status"] = "active";

            // Prepare the Lead object
            const createdLead = new this.leadModel({
                ...createLeadDto,
            });

            await createdLead.save();

            return new OkResponse({ message: "Lead created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createLeadFromZapier(fromZapierDto: Record<string, any>) {
        try {
            const companyId = "0f33b070-a7f2-43f3-8d07-54fdfd4378e3";
            const createdBy = "Zapier";
            const newLeadDate = new Date();
            const stageData = await this.crmStageModel.findOne({
                companyId,
                deleted: false,
                stageGroup: StageGroupEnum.Leads,
                sequence: 1,
            });
            const stageId = stageData._id;
            const csrId = stageData.defaultCsrId;
            const phone = fromZapierDto?.phone?.replace(/\s+/g, "").slice(-10);
            const email =
                fromZapierDto?.email && fromZapierDto?.email !== ""
                    ? fromZapierDto.email.toLowerCase()
                    : fromZapierDto?.emailLowerCase;
            const firstName =
                fromZapierDto?.firstName && fromZapierDto?.firstName !== ""
                    ? fromZapierDto.firstName
                    : fromZapierDto?.first_name && fromZapierDto?.first_name !== ""
                    ? fromZapierDto.first_name
                    : fromZapierDto?.full_name && fromZapierDto?.full_name !== ""
                    ? fromZapierDto.full_name.split(" ")[1]
                    : email
                    ? email?.split("@")[0]
                    : phone;

            const street = fromZapierDto?.address1 || "";
            const city = fromZapierDto?.city || "";
            const state = fromZapierDto?.state || "";
            const zip = fromZapierDto?.postalCode || "";

            const lastName =
                fromZapierDto?.lastName && fromZapierDto?.lastName !== ""
                    ? fromZapierDto.lastName
                    : fromZapierDto?.last_name && fromZapierDto?.last_name !== ""
                    ? fromZapierDto.last_name
                    : fromZapierDto?.full_name && fromZapierDto?.full_name !== ""
                    ? fromZapierDto.full_name.split(" ")[1]
                    : "";

            delete fromZapierDto?.address1;
            delete fromZapierDto?.postalCode;
            delete fromZapierDto?.emailLowerCase;
            delete fromZapierDto?.email;
            delete fromZapierDto?.phone;
            delete fromZapierDto?.first_name;
            delete fromZapierDto?.last_name;
            const tracking = {
                sessionSource: fromZapierDto?.contact_source || undefined,
                utmSource: fromZapierDto["Lead Source Description"] || undefined,
                utmMedium: "",
                utmContent: "",
                utmCampaign: fromZapierDto["Conversion - Campaign"] || undefined,
                utmTerm: "",
                utmKeyword: fromZapierDto["Conversion - Keywords"] || undefined,
                utmMatchType: "",
                referringWebpage: "",
                googleClickId: fromZapierDto["Google - Click ID"] || undefined,
                adSetId: fromZapierDto["Conversion - Ad Set"] || undefined,
                adName: fromZapierDto["Conversion - Creative"] || undefined,
                gaClientId: "",
                userAgent: "",
                url: "",
                ip: "",
                adGroupId: "",
                gbraId: "",
                wbraId: "",
                fbr: "",
                fbp: "",
                form: "",
                formId: "",
                createdAt: Date,
            };

            let contactId = randomUUID();
            const fullName = lastName && lastName !== "" ? firstName + " " + lastName : firstName;

            // check if contact already exists
            const existingContact = await this.contactModel.findOne({
                companyId,
                $or: [{ phone: phone }, { email: email }, { fullName: { $regex: fullName, $options: "i" } }],
                deleted: false,
            });
            if (existingContact) {
                contactId = existingContact._id;
            }

            const finalData: any = {
                ...fromZapierDto,
                firstName,
                lastName,
                contactId,
                email,
                street,
                city,
                state,
                zip,
                zapierLead: true,
                createdBy,
                stageId,
                newLeadDate,
                csrId,
                phone,
                tracking,
                rawTracking: { ...fromZapierDto },
            };

            const { data } = await this.createLead(finalData, companyId);

            const fullAddress = [street, city, state, zip].filter(Boolean).join(", ");

            const comments = [];
            // adding contact
            if (fromZapierDto["Contact Notes"] && fromZapierDto["Contact Notes"] !== "") {
                comments.push({
                    _id: randomUUID(),
                    body: fromZapierDto["Contact Notes"],
                    createdBy: createdBy,
                    createdAt: new Date(),
                    edits: [],
                });
            }
            if (fromZapierDto["Project Notes"] && fromZapierDto["Project Notes"] !== "") {
                comments.push({
                    _id: randomUUID(),
                    body: fromZapierDto["Project Notes"],
                    createdBy: createdBy,
                    createdAt: new Date(),
                    edits: [],
                });
            }

            if (existingContact) {
                // Prepare update data, only include non-empty fields
                const updateData: any = {
                    fullName: fullName.trim(),
                    ...(!existingContact.firstName &&
                        finalData?.firstName && { firstName: finalData.firstName }),
                    ...(!existingContact.lastName && finalData?.lastName && { lastName: finalData.lastName }),
                    ...(!existingContact.phone && finalData?.phone && { phone: finalData.phone }),
                    ...(!existingContact.email && finalData?.email && { email: finalData.email }),
                    ...(!existingContact.street && finalData?.street && { street: finalData.street }),
                    ...(!existingContact.city && finalData?.city && { city: finalData.city }),
                    ...(!existingContact.state && finalData?.state && { state: finalData.state }),
                    ...(!existingContact.zip && finalData?.zip && { zip: finalData.zip }),
                    ...(!existingContact.fullAddress && { fullAddress }),
                };

                // Add tracking data if it doesn't exist
                if (!existingContact?.tracking || !existingContact?.tracking?.length) {
                    updateData.tracking = [tracking];
                } else {
                    await this.contactModel.updateOne(
                        { _id: existingContact._id },
                        { $push: { tracking: tracking } },
                    );
                }

                // Create comment with imported information
                const importedFields = Object.entries(updateData)
                    .filter(([key]) => key !== "fullName" && key !== "tracking")
                    .map(([key, value]) => `${key}: ${value}`)
                    .join(", ");

                if (importedFields) {
                    comments.push({
                        _id: randomUUID(),
                        body: `Imported new information from Zapier lead: ${importedFields}`,
                        createdBy: createdBy,
                        createdAt: new Date(),
                        edits: [],
                    });
                }

                // Update contact with new data and comments
                await this.contactModel.updateOne(
                    { _id: existingContact._id },
                    {
                        $set: updateData,
                        $push: { comments: { $each: comments } },
                    },
                );
                // Creating activity data
                const createdOppActivity = new this.activityLogModel({
                    moduleId: existingContact._id,
                    moduleType: "contact",
                    companyId: companyId,
                    activities: [
                        {
                            _id: randomUUID(),
                            body: "imported new lead",
                            createdBy,
                            createdAt: new Date(),
                        },
                    ],
                });
                await createdOppActivity.save();
            } else {
                const contact = new this.contactModel({
                    _id: contactId,
                    companyId,
                    fullName: fullName.trim(),
                    firstName: finalData?.firstName,
                    lastName: finalData?.lastName,
                    phone: finalData?.phone,
                    email: finalData?.email,
                    street: finalData?.street,
                    city: finalData?.city,
                    state: finalData?.state,
                    zip: finalData?.zip,
                    fullAddress,
                    type: ContactTypeEnum.LEAD,
                    dateReceived: new Date(),
                    zapierLead: true,
                    createdBy,
                    stageId,
                    newLeadDate,
                    csrId,
                    comments,
                    tracking: [tracking],
                });
                await contact.save();
                // Creating activity data
                const createdOppActivity = new this.activityLogModel({
                    moduleId: contactId,
                    moduleType: "contact",
                    companyId: companyId,
                    activities: [
                        {
                            _id: randomUUID(),
                            body: "created a New Contact with Lead",
                            createdBy,
                            createdAt: new Date(),
                        },
                    ],
                });
                await createdOppActivity.save();
            }

            return new OkResponse({ message: data.message });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findAllLeads(
        companyId: string,
        { stageGroup, status, deleted }: FetchLeadDto,
        memberId: string,
        teamPermission: number,
    ): Promise<HttpResponse> {
        try {
            // To get list of members managed by logged in member
            const { members } = await this.positionService.getManagedMembersInternal(
                memberId,
                companyId,
                teamPermission,
            );

            const query = {
                companyId,
                csrId: { $in: members },
                ...(deleted !== undefined && { deleted }),
                ...(stageGroup !== undefined && { "stageData.stageGroup": stageGroup }),
                ...(status !== undefined && { status }),
            };
            const companySettings = await this.companySettingModel.findOne({ companyId });
            const weekEndDays = companySettings?.weekEndDays || [];
            const now = new Date();

            const leads = await this.leadModel.aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "Contact",
                        localField: "contactId",
                        foreignField: "_id",
                        as: "contactData",
                        pipeline: [
                            {
                                $project: {
                                    fullName: 1,
                                    businessName: 1,
                                    isBusiness: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "CrmStage",
                        localField: "stageId",
                        foreignField: "_id",
                        as: "stageData",
                        pipeline: [
                            {
                                $project: {
                                    _id: 1,
                                    code: 1,
                                    stageGroup: 1,
                                    agingCheckpointId: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "CrmCheckpoint",
                        localField: "stageData.agingCheckpointId",
                        foreignField: "_id",
                        as: "checkPointData",
                    },
                },
                {
                    $addFields: {
                        stageData: { $arrayElemAt: ["$stageData", 0] },
                        checkPointData: { $arrayElemAt: ["$checkPointData", 0] },
                        contactData: { $arrayElemAt: ["$contactData", 0] },
                    },
                },
                {
                    $addFields: {
                        fullName: {
                            $cond: [
                                { $eq: ["$contactData.isBusiness", true] },
                                "$contactData.businessName",
                                "$contactData.fullName",
                            ],
                        },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        deleted: 1,
                        stageId: 1,
                        stageData: 1,
                        checkPointData: 1,
                        contactId: 1,
                        fullName: 1,
                        newLeadDate: 1,
                        createdAt: 1,
                    },
                },
            ]);

            // Helper to calculate working days
            const getWorkingDaysDiff = (fromDate: Date, toDate: Date): number => {
                let count = 0;
                const current = new Date(fromDate);
                while (current <= toDate) {
                    const day = current.toLocaleString("en-US", { weekday: "long" });
                    if (!weekEndDays.includes(day)) {
                        count++;
                    }
                    current.setDate(current.getDate() + 1);
                }
                return count;
            };

            // Add agingVal
            const leadsWithAging = leads.map((lead) => {
                let agingVal: number | null = null;

                const symbol = lead.checkPointData?.symbol;
                const createdAt = lead.newLeadDate;

                if (symbol && createdAt) {
                    const createdDate = new Date(createdAt);
                    agingVal = getWorkingDaysDiff(createdDate, now);
                }

                return {
                    ...lead,
                    agingVal,
                };
            });
            return new OkResponse({ leads: leadsWithAging });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findLeadById(id: string, companyId: string, deleted: boolean): Promise<HttpResponse> {
        try {
            const result = await this.leadModel
                .aggregate([
                    {
                        $match: {
                            _id: id,
                            companyId,
                            deleted,
                        },
                    },
                    {
                        $lookup: {
                            from: "Referrers",
                            let: { referredBy: "$referredBy" },
                            pipeline: [
                                { $match: { $expr: { $eq: ["$_id", "$$referredBy"] } } },
                                { $project: { name: 1 } },
                            ],
                            as: "referrer",
                        },
                    },
                    {
                        $unwind: {
                            path: "$referrer",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            let: { memberArr: "$comments.createdBy" },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $in: ["$_id", "$$memberArr"] },
                                    },
                                },
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                    },
                                },
                            ],
                            as: "users",
                        },
                    },
                ])
                .exec();

            const leadData = result[0];

            if (!leadData) throw new NotFoundException("Lead not found");

            // Modify comments for name
            if (leadData && leadData.comments && leadData.comments.length > 0) {
                const usersMap = new Map(leadData.users.map((user) => [user._id, user]));
                leadData.comments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                });
            }
            return new OkResponse({ leadData });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateLead(id: string, companyId: string, updateLeadDto: UpdateLeadDto): Promise<HttpResponse> {
        try {
            const leadData = await this.leadModel.findOne({ _id: id, companyId, deleted: false });
            if (!leadData) throw new NotFoundException("Lead not found");

            // Sanitize the update data - only include fields that are defined in DTO and have values
            const sanitizedUpdate: Partial<UpdateLeadDto> = {};

            // Define the allowed fields from UpdateLeadDto
            const allowedFields: (keyof UpdateLeadDto)[] = [
                "leadSourceId",
                "campaignId",
                "referredBy",
                "workType",
                "newLeadDate",
            ];

            // Only include fields that are present and have meaningful values
            allowedFields.forEach((field) => {
                const value = updateLeadDto[field];
                if (value !== undefined && value !== "") {
                    sanitizedUpdate[field] = value;
                }
            });

            // Only proceed with update if there are fields to update
            if (Object.keys(sanitizedUpdate).length === 0) {
                return new OkResponse({ message: "No valid fields to update" });
            }

            await this.leadModel.updateOne({ _id: id, companyId }, { $set: sanitizedUpdate });
            return new OkResponse({ message: "Lead updated successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteLead(id: string, companyId: string) {
        try {
            const leadData = await this.leadModel.findOne({ _id: id, companyId });
            if (!leadData) throw new NotFoundException("Lead not found");
            if (leadData.deleted) {
                throw new BadRequestException("Lead is already deleted");
            }
            await this.leadModel.updateOne({ _id: id, companyId }, { deleted: true });
            return new OkResponse({ message: "Lead deleted successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreLead(id: string, companyId: string) {
        try {
            const leadData = await this.leadModel.findOne({ _id: id, companyId });
            if (!leadData) throw new NotFoundException("Lead not found");
            if (!leadData.deleted) {
                throw new BadRequestException("This lead is not deleted");
            }
            await this.leadModel.updateOne({ _id: id, companyId }, { $set: { deleted: false } });
            return new OkResponse({ message: "Lead restored successfully!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteLead(id: string, companyId: string) {
        try {
            const leadData = await this.leadModel.findOne({ _id: id, companyId });
            if (!leadData.deleted) {
                throw new BadRequestException("This lead is not deleted");
            }
            await this.leadModel.deleteOne({ _id: id, companyId });
            return new OkResponse({ message: "Lead deleted permanently!" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createNewAction(companyId: string, createNewActionDto: CreateNewLeadActionDto) {
        try {
            const result = await this.leadModel.updateOne(
                { _id: createNewActionDto.leadId, companyId },
                {
                    $set: {
                        nextAction: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            createdBy: createNewActionDto.memberId,
                            createdAt: createNewActionDto.currDate,
                        },
                        todoCheck: false,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Action created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async completeAction(companyId: string, createNewActionDto: CreateNewLeadActionDto) {
        try {
            const result = await this.leadModel.updateOne(
                { _id: createNewActionDto.leadId, companyId },
                {
                    $push: {
                        actions: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            completedBy: createNewActionDto.memberId,
                            completedAt: createNewActionDto.currDate,
                        },
                    },
                    todoCheck: true,
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createLeadComment(companyId: string, createLeadCommentDto: CreateLeadCommentDto) {
        try {
            const result = await this.leadModel.updateOne(
                { _id: createLeadCommentDto.leadId, companyId },
                {
                    $push: {
                        comments: {
                            _id: randomUUID(),
                            body: createLeadCommentDto.body,
                            createdBy: createLeadCommentDto.memberId,
                            createdAt: new Date(createLeadCommentDto.currDate),
                            edits: [],
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Comment created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // async updateLeadComment(companyId: string, updateLeadCommentDto: UpdateLeadCommentDto) {
    //     try {
    //         const { body, currDate, id, memberId, leadId } = updateLeadCommentDto;
    //         const lead = await this.leadModel
    //             .findOne({
    //                 _id: leadId,
    //                 companyId,
    //                 // comments: {
    //                 //     $elemMatch: {
    //                 //         _id: id,
    //                 //         createdBy: memberId,
    //                 //     },
    //                 // },
    //             })
    //             .select("comments");
    //         if (!lead) throw new HttpException("Lead not found", HttpStatus.BAD_REQUEST);

    //         const memberComment = lead.comments.find((c) => c._id === id);

    //         if (memberComment && memberComment.createdBy !== memberId)
    //             throw new BadRequestException("You can only edit your comments");

    //         if (memberComment && memberComment?.edits?.length >= 5)
    //             throw new BadRequestException("Maximum numbers of edit limit reached");

    //         if (
    //             memberComment &&
    //             Math.abs(new Date(memberComment?.createdAt).getTime() - new Date(currDate).getTime()) >
    //                 60 * 60 * 1000
    //         )
    //             throw new BadRequestException("You can't edit old comment");

    //         const changes = getTextChange({ body }, { body: memberComment.body });

    //         const edits: any[] = changes?.body
    //             ? [{ editedAt: new Date(currDate), edit: changes?.body }, ...memberComment?.edits]
    //             : [...memberComment?.edits];

    //         const result = await this.leadModel.updateOne(
    //             {
    //                 _id: leadId,
    //                 comments: {
    //                     $elemMatch: {
    //                         _id: id,
    //                         createdBy: memberId,
    //                     },
    //                 },
    //             },
    //             {
    //                 $set: {
    //                     "comments.$.body": body,
    //                     "comments.$.edits": edits,
    //                 },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
    //         }

    //         return new OkResponse({ message: "Lead comment updated successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async deleteLeadComment(userId: string, deleteLeadCommentDto: DeleteLeadCommentDto) {
        try {
            const lead = await this.leadModel.findOne({
                _id: deleteLeadCommentDto.leadId,
                comments: {
                    $elemMatch: {
                        _id: deleteLeadCommentDto.id,
                        createdBy: deleteLeadCommentDto.memberId,
                    },
                },
            });
            if (!lead) throw new HttpException("Lead not found", HttpStatus.BAD_REQUEST);

            const result = await this.leadModel.updateOne(
                {
                    _id: deleteLeadCommentDto.leadId,
                    comments: {
                        $elemMatch: {
                            _id: deleteLeadCommentDto.id,
                            createdBy: deleteLeadCommentDto.memberId,
                        },
                    },
                },
                {
                    $pull: {
                        comments: {
                            _id: deleteLeadCommentDto.id,
                            createdBy: deleteLeadCommentDto.memberId,
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "Lead Comment deleted successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateChecklist(userId: string, updateChecklistDto: UpdateLeadChecklistDto) {
        try {
            let placeInLine = undefined;
            if (updateChecklistDto.key === "place-in-line-result") placeInLine = updateChecklistDto.value;
            const updateObj = {
                boolean: updateChecklistDto.boolean,
                value: updateChecklistDto?.value,
            };
            const result = await this.leadModel.updateOne(
                { _id: updateChecklistDto.leadId, deleted: false },
                {
                    $set: {
                        [`stepsChecklist.${updateChecklistDto.stage}.${updateChecklistDto.key}`]: updateObj,
                        placeInLine,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Lead checklist updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateLeadActivity(companyId: string, updateLeadActivityDto: UpdateLeadActivityDto) {
        try {
            const activity = await this.activityLogModel.updateOne(
                { moduleId: updateLeadActivityDto.id, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: updateLeadActivityDto.body,
                            createdBy: updateLeadActivityDto.memberId,
                            createdAt: updateLeadActivityDto.currDate,
                        },
                    },
                },
                { upsert: true, new: true },
            );

            return new OkResponse({ data: activity, message: "Opportunity activity updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async leadInvalidStatus(
        leadId: string,
        companyId: string,
        loginMemberId: string,
        updateLeadStatusDto: UpdateLeadStatusDto,
    ) {
        try {
            const result = await this.leadModel.findOneAndUpdate(
                { _id: leadId, companyId },
                {
                    $set: {
                        status: "invalid",
                        invalidLeadReason: updateLeadStatusDto?.invalidLeadReason,
                    },
                    $push: {
                        statusChanges: {
                            status: "invalid",
                            statusChangedDate: new Date(),
                            statusChangedBy: loginMemberId,
                        },
                    },
                },
            );

            if (!result) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            await this.activityLogModel.updateOne(
                { moduleId: result?.contactId, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: `Invalidated the Lead`,
                            createdBy: loginMemberId,
                            createdAt: new Date(),
                        },
                    },
                },
            );

            return new OkResponse({ message: "Lead status changed successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // ststus active
    async activeLead(leadId: string, companyId: string, loginMemberId: string) {
        try {
            const result = await this.leadModel.updateOne(
                { _id: leadId, companyId },
                {
                    $set: {
                        status: OpportunityStatusEnum.Active,
                    },
                    $push: {
                        statusChanges: {
                            status: OpportunityStatusEnum.Active,
                            statusChangedDate: new Date(),
                            statusChangedBy: loginMemberId,
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Lead status changed successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async lostLead(
        companyId: string,
        memberId: string,
        leadId: string,
        lostUnLostLeadDto: LostUnLostLeadDto,
    ) {
        try {
            const { date, reason } = lostUnLostLeadDto;

            // Fetch checkpoints
            const checkpoints = await this.crmCheckpointModel.find({ companyId }).select("symbol name");
            const symbolNames = checkpoints.map((c) => c.symbol).join(" ");

            // Fetch lead with selected symbols
            const lead = await this.leadModel.findOne({ _id: leadId, companyId }).select(symbolNames);
            if (!lead) throw new HttpException("Lead not found!", HttpStatus.BAD_REQUEST);

            // Prepare unset data
            let rmvCheckName = "";
            const unsetData = checkpoints.reduce((acc, { symbol, name }) => {
                if (lead[symbol] && new Date(lead[symbol]) > new Date(date)) {
                    acc[symbol] = 1;
                    rmvCheckName += ` & ${name} date ${formatDate(lead[symbol])} removed`;
                }
                return acc;
            }, {} as Record<string, number>);

            // Update Lead
            const result = await this.leadModel.findOneAndUpdate(
                { _id: leadId, companyId },
                {
                    $set: {
                        lostReason: reason,
                        status: OpportunityStatusEnum.Lost,
                        lostDate: date,
                        lostBy: memberId,
                        "checkpointActivity.lostDate": {
                            created: new Date(date),
                        },
                    },
                    $unset: unsetData,
                },
            );

            if (!result) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            const activityBody = Object.keys(unsetData).length ? reason + rmvCheckName : reason;

            await this.activityLogModel.updateOne(
                { moduleId: result?.contactId, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: `Lost the Lead -> ${activityBody}`,
                            createdBy: memberId,
                            createdAt: lostUnLostLeadDto.date,
                        },
                    },
                },
            );

            return new OkResponse({ message: "Lead lost status set to true" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async unLostLead(
        companyId: string,
        memberId: string,
        leadId: string,
        lostUnLostLeadDto: LostUnLostLeadDto,
    ) {
        try {
            const result = await this.leadModel.findOneAndUpdate(
                { _id: leadId, companyId },
                {
                    $unset: { lostDate: 1, lostBy: 1, lostReason: 1 },
                    $set: {
                        unLostReason: lostUnLostLeadDto.reason,
                        status: OpportunityStatusEnum.Active,
                        unLostDate: lostUnLostLeadDto.date,
                        unLostBy: memberId,
                        "checkpointActivity.unLostDate": {
                            created: new Date(lostUnLostLeadDto.date),
                        },
                    },
                },
            );

            if (!result) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            await this.activityLogModel.updateOne(
                { moduleId: result?.contactId, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: `Un-Lost the Lead -> ${lostUnLostLeadDto.reason}`,
                            createdBy: memberId,
                            createdAt: lostUnLostLeadDto.date,
                        },
                    },
                },
            );

            return new OkResponse({ message: "Lead lost status set to false" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLeadCompletionPercent(
        userId: string,
        companyId: string,
        deleted: boolean,
        getLeadDto: GetLeadDto,
    ) {
        try {
            const { stageGroup, csrId, lost, status } = getLeadDto;
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setMonth(thirtyDaysAgo.getMonth() - 1);

            const query: any = {
                $and: [
                    {
                        $or: [
                            {
                                "stage.code": "completed",
                                jobCompletedDate: { $gte: thirtyDaysAgo },
                            },
                            { "stage.code": { $ne: "completed" } },
                        ],
                    },
                ],
                companyId,
                deleted,
                ...(stageGroup !== undefined && { "stage.stageGroup": stageGroup }),
                ...(csrId && { csrId }),
                // ...(projectManager && { projectManager }),
                ...(lost !== undefined && { lost }),
            };

            if (status === "active") {
                query.status = { $ne: "inactive" };
            } else if (status === "inactive") {
                query.status = { $eq: "inactive" };
            }

            const [lead, allStep, allCheckpoints] = await Promise.all([
                this.leadModel
                    .aggregate([
                        {
                            $lookup: {
                                from: "CrmStage",
                                localField: "stage",
                                foreignField: "_id",
                                as: "stage",
                            },
                        },
                        {
                            $unwind: {
                                path: "$stage",
                                preserveNullAndEmptyArrays: false,
                            },
                        },

                        { $match: query },
                        { $project: { stepsChecklist: 1, stage: 1, workType: 1 } },
                    ])
                    .exec(),
                this.crmStepModel.find({ companyId, deleted: false }),
                this.crmCheckpointModel.find({ companyId, deleted: false }),
            ]);

            const completed = lead.map((opp) => {
                const stage = opp.stage;
                const step = allStep
                    .filter((s) => {
                        return (
                            s.stageId === stage._id &&
                            (!s.projectTypeId.length || s.projectTypeId.includes(opp.oppType)) &&
                            (!s.location.length || s.location.includes(opp.state))
                        );
                    })
                    .map((s) => s._id);

                const checkpoints = allCheckpoints
                    .filter((c) => c.stageSet.includes(stage._id))
                    .map((c) => c._id);

                const combinedCheckList = [...step, ...checkpoints];
                const stepCompleted = (opp?.stepsChecklist && opp.stepsChecklist?.[`${stage?._id}`]) || {};

                let count = 0;
                for (const key in stepCompleted) {
                    if (combinedCheckList.includes(key) && stepCompleted[key]?.boolean === true) {
                        count++;
                    }
                }

                const percentDone = combinedCheckList.length ? (count / combinedCheckList.length) * 100 : 0;
                return {
                    percent_done: roundTo2(percentDone),
                    _id: opp._id,
                };
            });

            return new OkResponse({
                completed,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLeadStepChecklistByStage(_id: string, stageId: string) {
        try {
            const checklistField = `stepsChecklist.${[stageId]}`;
            const selectObject = {};
            selectObject[checklistField] = 1;

            const checklist = await this.leadModel.findOne({ _id }).select(selectObject);

            return new OkResponse({ checklist });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // async convertToOpportunity(
    //     companyId: string,
    //     memberId: string,
    //     leadId: string,
    //     opportunityId: string,
    //     date: Date,
    // ) {
    //     try {
    //         const leadData = await this.leadModel.findOneAndUpdate(
    //             { _id: leadId, companyId },
    //             { $set: { oppId: opportunityId, oppDate: date } },
    //         );
    //         const oppDataToUpdate = {
    //             csrId: leadData?.csrId,
    //             lostReason: leadData?.lostReason,
    //             lost: leadData?.lost,
    //             lostDate: leadData?.lostDate,
    //             unLostReason: leadData?.unLostReason,
    //             unLostDate: leadData?.unLostDate,
    //             unLostBy: leadData?.unLostBy,
    //             lostBy: leadData?.lostBy,
    //             checkpointActivity: leadData?.checkpointActivity,
    //             actions: leadData?.actions,
    //             nextAction: leadData?.nextAction,
    //             todoCheck: leadData?.todoCheck,
    //             stepsChecklist: leadData?.stepsChecklist,
    //             leadId,
    //         };
    //         await this.opportunityModel.updateOne(
    //             { _id: opportunityId },
    //             {
    //                 $set: oppDataToUpdate,
    //                 $push: {
    //                     comments: {
    //                         $each: leadData?.comments || [], // Use $each to push multiple comments if they exist
    //                     },
    //                 },
    //             },
    //         );
    //         await this.activityModel.updateOne(
    //             { oppId: leadId },
    //             { $set: { oppId: opportunityId } },
    //             {
    //                 $push: {
    //                     activities: {
    //                         _id: randomUUID(),
    //                         body: "Lead converted to Opportunity",
    //                         createdBy: memberId,
    //                         createdAt: date,
    //                     },
    //                 },
    //             },
    //         );
    //         return new OkResponse({ message: "Lead to Oppotunity converted successfully" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async updateLeadCheckpoint(companyId: string, updateDto: UpdateOpportunityCheckpointDto) {
        try {
            //finding checkpoint
            const { data } = await this.crmService.getCheckpointById(
                companyId,
                updateDto.checkpointId,
                false,
            );
            if (!data.checkpoint) throw new Error("Checkpoint not found");

            const result = await this.leadModel.findOneAndUpdate(
                { _id: updateDto.id, companyId },
                {
                    $set: {
                        updatedAt: new Date(),
                        [data.checkpoint.symbol]: updateDto.currDate,
                        [`checkpointActivity.${data.checkpoint.symbol}`]: {
                            created: new Date(updateDto.currDate),
                        },
                    },
                },
                { upsert: true, new: true },
            );

            if (!result) {
                throw new BadRequestException("Failed to update changes!");
            }

            return new OkResponse({ message: "Lead checkpoint updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateLeadStage(companyId: string, memberId: string, leadId: string, stageId: string) {
        try {
            const stage = await this.crmStageModel.findOne({ _id: stageId, companyId });
            if (!stage) throw new BadRequestException("Stage not found");
            const result = await this.leadModel.findOneAndUpdate(
                { _id: leadId },
                {
                    $set: {
                        stageId: stageId,
                    },
                },
            );

            if (!result) {
                throw new BadRequestException("Failed to update changes!");
            }

            const body = `changed lead stage to ${stage.name}`;
            await this.activityLogModel.updateOne(
                { moduleId: result?.contactId, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: body,
                            createdBy: memberId,
                            createdAt: new Date(),
                        },
                    },
                },
            );
            return new OkResponse({ message: "Lead stage changed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteLeadCheckpoint(companyId: string, deleteDto: DeleteOpportunityCheckpointDto) {
        try {
            const { symbol, id, memberId, date, name } = deleteDto;
            // // finding lead by id and companyId
            // const lead = await this.leadModel.findOne({ _id: id, companyId });
            // if (!lead) {
            //     throw new Error(`Lead not found for company ${companyId}`);
            // }

            // const { data } = await this.crmService.getCheckpointById(userId, companyId, checkpointId, false);
            // if (!data.checkpoint) throw new Error("checkpoint not found");
            const result = await this.leadModel.updateOne(
                { _id: id, companyId },
                {
                    $set: {
                        [`checkpointActivity.${symbol}.deleted`]: new Date(date),
                    },
                    $unset: {
                        [symbol]: 1,
                    },
                },
            );

            const body = `${name} Checkpoint Date is deleted`;
            this.opportunityService.updateOpportunityActivity(companyId, {
                memberId,
                id,
                body,
                currDate: new Date(date),
            });

            // delete lead.checkpoint[data.checkpoint.name];
            // lead.markModified("checkpoint");
            // await lead.save();

            if (result.modifiedCount === 0) throw new BadRequestException("Failed to update changes!");
            else return new OkResponse({ message: `Checkpoint deleted successfully` });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
