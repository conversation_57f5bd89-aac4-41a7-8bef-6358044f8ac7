import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { UserModule } from "./user/user.module";
import { AuthModule } from "./auth/auth.module";
import { MailerModule } from "@nestjs-modules/mailer";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { PositionModule } from "./position/position.module";
import { CompensationModule } from "./compensation/compensation.module";
import { PayScheduleModule } from "./pay-schedule/pay-schedule.module";
import { DepartmentModule } from "./department/department.module";
import { RoleModule } from "./role/role.module";
import { CrewModule } from "./crew/crew.module";
import { PieceWorkModule } from "./piece-work/piece-work.module";
import { TimeCardModule } from "./time-card/time-card.module";
import * as Joi from "@hapi/joi";
import { DailyLogModule } from "./daily-log/daily-log.module";
import { SubcontractorsModule } from "./subcontractor/subcontractor.module";
import { WorkTaskModule } from "./work-task/work-task.module";
import { CrmModule } from "./crm/crm.module";
import { ClientModule } from "./client/client.module";
import { MarketingSettingModule } from "./marketing-setting/marketing-setting.module";
import { ReportModule } from "./report/report.module";
import { ProjectModule } from "./project/project.module";
import { DashboardModule } from "./dashboard/dashboard.module";
import { PayrollModule } from "./payroll/payroll.module";
import { GpsModule } from "./gps/gps.module";
import { CustomProjectModule } from "./custom-project/custom-project.module";
import { StripeModule } from "./stripe/stripe.module";
import { SubscriptionModule } from "./subscription/subscription.module";
import { AdminModule } from "./admin/admin.module";
import { CronModule } from "./cron/cron.module";
import { SupportModule } from "./support/support.module";
import { S3Module } from "./s3/s3.module";
import { LeadModule } from "./lead/lead.module";
import { OpportunityModule } from "./opportunity/opportunity.module";
import mongoose from "mongoose";
import { applyGlobalPlugins } from "./shared/mongoose plugins/mongoose.plugins";
import { FormBuilderModule } from "./form-builder/form-builder.module";
import { MediaModule } from "./media/media.module";
import { ContactsModule } from "./contacts/contacts.module";

@Module({
    imports: [
        ConfigModule.forRoot({
            envFilePath: [`.env`],
            validationSchema: Joi.object({}),
        }),
        MongooseModule.forRootAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => {
                // Apply global mongo plugins before connecting
                mongoose.plugin(applyGlobalPlugins);
                return {
                    uri: configService.get<string>("MONGO_URI"),
                };
            },
            inject: [ConfigService],
        }),
        UserModule,
        AuthModule,
        MailerModule,
        PositionModule,
        CompensationModule,
        PayScheduleModule,
        DepartmentModule,
        RoleModule,
        CrewModule,
        PieceWorkModule,
        TimeCardModule,
        DailyLogModule,
        SubcontractorsModule,
        WorkTaskModule,
        CrmModule,
        ClientModule,
        MarketingSettingModule,
        ReportModule,
        ProjectModule,
        DashboardModule,
        PayrollModule,
        GpsModule,
        CustomProjectModule,
        StripeModule,
        SubscriptionModule,
        AdminModule,
        CronModule,
        SupportModule,
        S3Module,
        LeadModule,
        OpportunityModule,
        FormBuilderModule,
        MediaModule,
        ContactsModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule {}
