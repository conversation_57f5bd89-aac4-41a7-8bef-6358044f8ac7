import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { PositionSchema } from "src/position/schema/position.schema";
import { LeadSourceController } from "./lead-source.controller";
import { LeadSourceService } from "./lead-source.service";
import { LeadSourceSchema } from "./schema/lead-source.schema";
import { CampaignService } from "./campaign.service";
import { CampaignController } from "./campaign.controller";
import { CampaignSchema } from "./schema/campaign.schema";
import { MarketingChannelSchema } from "./schema/channel.schema.dto";
import { ChannelController } from "./channel.controller";
import { ChannelService } from "./channel.service";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "LeadSource", schema: LeadSourceSchema },
            { name: "MarketingChannel", schema: MarketingChannelSchema },
            { name: "Position", schema: PositionSchema },
            { name: "Campaign", schema: CampaignSchema },
        ]),
    ],
    providers: [LeadSourceService, CampaignService, ChannelService],
    controllers: [LeadSourceController, CampaignController, ChannelController],
    exports: [LeadSourceService, CampaignService, ChannelService],
})
export class MarketingSettingModule {}
